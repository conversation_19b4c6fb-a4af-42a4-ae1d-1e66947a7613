import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      hidden: true,
      requiresAuth: false
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: Layout,
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '首页',
          icon: 'dashboard'
        }
      }
    ]
  },
  {
    path: '/listener',
    name: 'Listener',
    component: Layout,
    redirect: '/listener/index',
    meta: {
      title: '监听器管理',
      icon: 'listener',
      requiresAuth: true
    },
    children: [
      {
        path: 'index',
        name: 'ListenerIndex',
        component: () => import('@/views/listener/index.vue'),
        meta: {
          title: '监听器管理',
          icon: 'listener'
        }
      }
    ]
  },
  {
    path: '/client',
    name: 'Client',
    component: Layout,
    redirect: '/client/index',
    meta: {
      title: '客户端管理',
      icon: 'client',
      requiresAuth: true
    },
    children: [
      {
        path: 'index',
        name: 'ClientIndex',
        component: () => import('@/views/client/index.vue'),
        meta: {
          title: '客户端管理',
          icon: 'client'
        }
      }
    ]
  },
  {
    path: '/download',
    name: 'Download',
    component: Layout,
    redirect: '/download/index',
    meta: {
      title: '下载管理',
      icon: 'download',
      requiresAuth: true
    },
    children: [
      {
        path: 'index',
        name: 'DownloadIndex',
        component: () => import('@/views/download/DownloadManager.vue'),
        meta: {
          title: '下载管理',
          icon: 'download'
        }
      }
    ]
  },
  {
    path: '/screenshot',
    name: 'Screenshot',
    component: Layout,
    redirect: '/screenshot/index',
    meta: {
      title: '截图库',
      icon: 'camera',
      requiresAuth: true
    },
    children: [
      {
        path: 'index',
        name: 'ScreenshotIndex',
        component: () => import('@/views/screenshot/index.vue'),
        meta: {
          title: '截图库',
          icon: 'camera'
        }
      }
    ]
  },
  {
    path: '/proxy',
    name: 'Proxy',
    component: Layout,
    redirect: '/proxy/index',
    meta: {
      title: '代理管理',
      icon: 'proxy',
      requiresAuth: true
    },
    children: [
      {
        path: 'index',
        name: 'ProxyIndex',
        component: () => import('@/views/proxy/index.vue'),
        meta: {
          title: '代理实例',
          icon: 'proxy',
          requiresAuth: true
        }
      },
      {
        path: 'chain',
        name: 'ProxyChain',
        component: () => import('@/views/proxy/chain.vue'),
        meta: {
          title: '代理链',
          icon: 'chain',
          requiresAuth: true
        }
      },
      {
        path: 'monitor',
        name: 'ProxyMonitor',
        component: () => import('@/views/proxy/monitor.vue'),
        meta: {
          title: '监控统计',
          icon: 'monitor',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/user-manage',
    name: 'UserManage',
    component: Layout,
    redirect: '/user-manage/index',
    meta: {
      title: '用户管理',
      icon: 'user',
      requiresAuth: true,
      requiresAdmin: true  // 只有管理员可以访问
    },
    children: [
      {
        path: 'index',
        name: 'UserManageIndex',
        component: () => import('@/views/userManage/index.vue'),
        meta: {
          title: '用户管理',
          icon: 'user',
          requiresAdmin: true
        }
      }
    ]
  },
  {
    path: '/log',
    name: 'Log',
    component: Layout,
    redirect: '/log/index',
    meta: {
      title: '日志监控',
      icon: 'log',
      requiresAuth: true
    },
    children: [
      {
        path: 'index',
        name: 'LogIndex',
        component: () => import('@/views/log/index.vue'),
        meta: {
          title: '日志监控',
          icon: 'log'
        }
      }
    ]
  },
  {
    path: '/performance',
    name: 'Performance',
    component: Layout,
    redirect: '/performance/index',
    meta: {
      title: '性能监控',
      icon: 'performance',
      requiresAuth: true
    },
    children: [
      {
        path: 'index',
        name: 'PerformanceIndex',
        component: () => import('@/views/performance/index.vue'),
        meta: {
          title: '性能监控',
          icon: 'performance'
        }
      }
    ]
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Layout,
    redirect: '/settings/index',
    meta: {
      title: '系统设置',
      icon: 'settings',
      requiresAuth: true
    },
    children: [
      {
        path: 'index',
        name: 'SettingsIndex',
        component: () => import('@/views/settings/index.vue'),
        meta: {
          title: '系统设置',
          icon: 'settings'
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const token = localStorage.getItem('token')

  // 需要登录的页面
  if (to.meta.requiresAuth) {
    if (token) {
      // 检查是否需要管理员权限
      if (to.meta.requiresAdmin) {
        try {
          // 动态导入auth API避免循环依赖
          const { getUserInfo } = await import('@/api/auth')
          const response = await getUserInfo()

          if (response.code === 200 && response.data.isAdmin) {
            next()
          } else {
            // 不是管理员，重定向到首页
            next('/')
          }
        } catch (error) {
          console.error('权限检查失败:', error)
          next('/login')
        }
      } else {
        next()
      }
    } else {
      next('/login')
    }
  } else {
    // 已登录状态下访问登录页，重定向到首页
    if (to.path === '/login' && token) {
      next('/')
    } else {
      next()
    }
  }
})

export default router