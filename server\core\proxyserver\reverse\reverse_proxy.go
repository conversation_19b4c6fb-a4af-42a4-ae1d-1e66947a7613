// reverse_proxy.go - 基于iox-master的反向代理实现
// 使用smux多路复用，避免连接管理问题
package reverse

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net"
	"server/global"
	"sync"
	"sync/atomic"
	"time"
	"server/model/basic"
	"github.com/xtaci/smux"
	"go.uber.org/zap"
)

// 基于iox-master的协议定义
const (
	CTL_HANDSHAKE = iota
	CTL_CONNECT_ME
	CTL_CLEANUP

	MAX_CONNECTION   = 0x800
	CLIENT_HANDSHAKE = 0xC0
	SERVER_HANDSHAKE = 0xE0
)

// 协议结构
type Protocol struct {
	CMD byte
	N   byte
}

var PROTO_END = []byte{0xEE, 0xFF}

// 协议编解码函数
func marshal(p Protocol) []byte {
	buf := make([]byte, 4)
	buf[0] = p.CMD
	buf[1] = p.N
	buf[2], buf[3] = PROTO_END[0], PROTO_END[1]
	return buf
}

func unmarshal(b []byte) Protocol {
	return Protocol{
		CMD: b[0],
		N:   b[1],
	}
}

func bytesEq(a, b []byte) bool {
	for i := 0; i < len(a); i++ {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func readUntilEnd(conn net.Conn) ([]byte, error) {
	buf := make([]byte, 1)
	output := make([]byte, 0, 4)

	for {
		n, err := conn.Read(buf)
		if err != nil {
			return nil, err
		}

		if n != 1 || len(output) > 4 {
			return nil, errors.New("transmission error")
		}

		output = append(output, buf[0])

		if len(output) == 4 && bytesEq(PROTO_END, output[len(output)-2:]) {
			break
		}
	}

	return output[:2], nil
}

// ReverseProxyServer 反向代理服务器
type ReverseProxyServer struct {
	proxyID    string
	name       string
	userPort   int
	clientPort int

	userListener   net.Listener
	clientListener net.Listener

	session    *smux.Session
	ctlStream  *smux.Stream

	userConnBuffer chan net.Conn

	ctx    context.Context
	cancel context.CancelFunc

	stats struct {
		totalConnections  int64
		activeConnections int64
		bytesTransferred  int64
	}

	mu sync.RWMutex
}

// NewReverseProxyServer 创建反向代理服务器
func NewReverseProxyServer(proxyID, name string, userPort, clientPort int) *ReverseProxyServer {
	ctx, cancel := context.WithCancel(context.Background())

	return &ReverseProxyServer{
		proxyID:        proxyID,
		name:           name,
		userPort:       userPort,
		clientPort:     clientPort,
		userConnBuffer: make(chan net.Conn, MAX_CONNECTION),
		ctx:            ctx,
		cancel:         cancel,
	}
}

// Start 启动反向代理服务器
func (s *ReverseProxyServer) Start() error {
	// 启动客户端监听器
	clientListener, err := net.Listen("tcp", fmt.Sprintf(":%d", s.clientPort))
	if err != nil {
		return fmt.Errorf("启动客户端监听器失败: %v", err)
	}
	s.clientListener = clientListener

	if global.LOG != nil {
		global.LOG.Info("🌐 [REVERSE-SERVER] 启动客户端监听器",
			zap.String("clientAddr", fmt.Sprintf(":%d", s.clientPort)),
			zap.Int("clientPort", s.clientPort))
	}

	// 启动用户监听器
	userListener, err := net.Listen("tcp", fmt.Sprintf(":%d", s.userPort))
	if err != nil {
		clientListener.Close()
		return fmt.Errorf("启动用户监听器失败: %v", err)
	}
	s.userListener = userListener

	if global.LOG != nil {
		global.LOG.Info("🌐 [REVERSE-SERVER] 启动用户监听器",
			zap.String("userAddr", fmt.Sprintf(":%d", s.userPort)),
			zap.Int("userPort", s.userPort))
	}

	// 等待客户端连接并建立session
	go s.handleClientConnection()

	// 处理用户连接
	go s.handleUserConnections()

	if global.LOG != nil {
		global.LOG.Info("✅ [REVERSE-SERVER] 反向代理启动成功",
			zap.String("proxyID", s.proxyID),
			zap.String("name", s.name),
			zap.Int("userPort", s.userPort),
			zap.Int("clientPort", s.clientPort))
	}

	return nil
}

// Stop 停止反向代理服务器
func (s *ReverseProxyServer) Stop() error {
	s.cancel()

	if s.userListener != nil {
		s.userListener.Close()
	}

	if s.clientListener != nil {
		s.clientListener.Close()
	}

	if s.ctlStream != nil {
		// 发送清理信号
		s.ctlStream.Write(marshal(Protocol{
			CMD: CTL_CLEANUP,
			N:   0,
		}))
		s.ctlStream.Close()
	}

	if s.session != nil {
		s.session.Close()
	}

	close(s.userConnBuffer)

	if global.LOG != nil {
		global.LOG.Info("🔴 [REVERSE-SERVER] 反向代理已停止", zap.String("proxyID", s.proxyID))
	}

	return nil
}

// GetStats 获取统计信息
func (s *ReverseProxyServer) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"total_connections":  atomic.LoadInt64(&s.stats.totalConnections),
		"active_connections": atomic.LoadInt64(&s.stats.activeConnections),
		"bytes_transferred":  atomic.LoadInt64(&s.stats.bytesTransferred),
	}
}

// UpdateDatabase 更新数据库统计
func (s *ReverseProxyServer) UpdateDatabase() error {
	if global.DB == nil {
		return errors.New("数据库连接不可用")
	}

	stats := s.GetStats()

	return global.DB.Model(&basic.Proxy{}).
		Where("proxy_id = ?", s.proxyID).
		Updates(map[string]interface{}{
			"total_connections":  stats["total_connections"],
			"active_connections": stats["active_connections"],
			"bytes_transferred":  stats["bytes_transferred"],
			"last_activity":      time.Now(),
		}).Error
}

// serverHandshake 服务器端握手
func (s *ReverseProxyServer) serverHandshake() (*smux.Session, *smux.Stream, error) {
	var conn net.Conn
	var session *smux.Session
	var ctlStream *smux.Stream
	var err error

	for {
		select {
		case <-s.ctx.Done():
			return nil, nil, errors.New("服务已停止")
		default:
		}

		conn, err = s.clientListener.Accept()
		if err != nil {
			continue
		}

		session, err = smux.Server(conn, &smux.Config{
			Version:           2,
			KeepAliveInterval: 30 * time.Second,
			KeepAliveTimeout:  60 * time.Second,
			MaxFrameSize:      32768,
			MaxReceiveBuffer:  4194304,
			MaxStreamBuffer:   65536,
		})
		if err != nil {
			conn.Close()
			continue
		}

		ctlStream, err = session.AcceptStream()
		if err != nil {
			session.Close()
			continue
		}

		pb, err := readUntilEnd(ctlStream)
		if err != nil {
			ctlStream.Close()
			session.Close()
			continue
		}

		p := unmarshal(pb)
		if p.CMD == CTL_HANDSHAKE && p.N == CLIENT_HANDSHAKE {
			ctlStream.Write(marshal(Protocol{
				CMD: CTL_HANDSHAKE,
				N:   SERVER_HANDSHAKE,
			}))
			break
		}

		ctlStream.Close()
		session.Close()
	}

	return session, ctlStream, nil
}

// handleClientConnection 处理客户端连接
func (s *ReverseProxyServer) handleClientConnection() {
	session, ctlStream, err := s.serverHandshake()
	if err != nil {
		if global.LOG != nil {
			global.LOG.Error("客户端握手失败", zap.Error(err))
		}
		return
	}

	s.mu.Lock()
	s.session = session
	s.ctlStream = ctlStream
	s.mu.Unlock()

	if global.LOG != nil {
		global.LOG.Info("✅ [REVERSE-SERVER] 客户端握手成功",
			zap.String("remoteAddr", session.RemoteAddr().String()))
	}

	// 处理控制流读取
	go func() {
		defer func() {
			s.mu.Lock()
			s.session = nil
			s.ctlStream = nil
			s.mu.Unlock()
		}()

		for {
			select {
			case <-s.ctx.Done():
				return
			default:
			}

			pb, err := readUntilEnd(ctlStream)
			if err != nil {
				if global.LOG != nil {
					global.LOG.Warn("控制连接已断开", zap.Error(err))
				}
				return
			}

			p := unmarshal(pb)
			switch p.CMD {
			case CTL_CLEANUP:
				if global.LOG != nil {
					global.LOG.Info("收到客户端清理信号")
				}
				return
			}
		}
	}()

	// 处理数据流
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
		}

		remoteStream, err := session.AcceptStream()
		if err != nil {
			if global.LOG != nil {
				global.LOG.Warn("接受数据流失败", zap.Error(err))
			}
			continue
		}

		// 从缓冲区获取用户连接
		select {
		case userConn := <-s.userConnBuffer:
			go s.forwardConnections(userConn, remoteStream)
		case <-time.After(30 * time.Second):
			remoteStream.Close()
			if global.LOG != nil {
				global.LOG.Warn("等待用户连接超时")
			}
		case <-s.ctx.Done():
			remoteStream.Close()
			return
		}
	}
}

// handleUserConnections 处理用户连接
func (s *ReverseProxyServer) handleUserConnections() {
	for {
		select {
		case <-s.ctx.Done():
			return
		default:
		}

		userConn, err := s.userListener.Accept()
		if err != nil {
			continue
		}

		atomic.AddInt64(&s.stats.totalConnections, 1)
		atomic.AddInt64(&s.stats.activeConnections, 1)

		// 将用户连接放入缓冲区
		select {
		case s.userConnBuffer <- userConn:
			// 通知客户端建立连接
			s.mu.RLock()
			ctlStream := s.ctlStream
			s.mu.RUnlock()

			if ctlStream != nil {
				_, err = ctlStream.Write(marshal(Protocol{
					CMD: CTL_CONNECT_ME,
					N:   1,
				}))
				if err != nil {
					if global.LOG != nil {
						global.LOG.Warn("发送连接请求失败", zap.Error(err))
					}
					userConn.Close()
					atomic.AddInt64(&s.stats.activeConnections, -1)
				}
			} else {
				userConn.Close()
				atomic.AddInt64(&s.stats.activeConnections, -1)
				if global.LOG != nil {
					global.LOG.Warn("控制连接不可用")
				}
			}
		case <-time.After(5 * time.Second):
			userConn.Close()
			atomic.AddInt64(&s.stats.activeConnections, -1)
			if global.LOG != nil {
				global.LOG.Warn("用户连接缓冲区已满")
			}
		case <-s.ctx.Done():
			userConn.Close()
			return
		}
	}
}

// forwardConnections 转发用户连接和客户端流
func (s *ReverseProxyServer) forwardConnections(userConn net.Conn, remoteStream net.Conn) {
	defer func() {
		userConn.Close()
		remoteStream.Close()
		atomic.AddInt64(&s.stats.activeConnections, -1)
	}()

	if global.LOG != nil {
		global.LOG.Info("🔄 [REVERSE-SERVER] 开始转发连接",
			zap.String("userAddr", userConn.RemoteAddr().String()))
	}

	// 双向转发
	done := make(chan struct{}, 2)

	// 用户 -> 客户端
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(remoteStream, userConn)
		if err != nil && global.LOG != nil {
			global.LOG.Debug("用户->客户端转发结束", zap.Error(err))
		}
		atomic.AddInt64(&s.stats.bytesTransferred, written)
	}()

	// 客户端 -> 用户
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(userConn, remoteStream)
		if err != nil && global.LOG != nil {
			global.LOG.Debug("客户端->用户转发结束", zap.Error(err))
		}
		atomic.AddInt64(&s.stats.bytesTransferred, written)
	}()

	// 等待任一方向完成
	<-done

	if global.LOG != nil {
		global.LOG.Info("🔚 [REVERSE-SERVER] 连接转发结束",
			zap.String("userAddr", userConn.RemoteAddr().String()))
	}
}

// ReverseProxyManager 反向代理管理器
type ReverseProxyManager struct {
	servers map[string]*ReverseProxyServer
	mu      sync.RWMutex
}

// GlobalReverseManager 全局反向代理管理器
var GlobalReverseManager = &ReverseProxyManager{
	servers: make(map[string]*ReverseProxyServer),
}

// StartServer 启动反向代理服务器
func (rm *ReverseProxyManager) StartServer(proxy *basic.Proxy) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if proxy.ProxyID == "" {
		return fmt.Errorf("ProxyID不能为空")
	}

	server := NewReverseProxyServer(proxy.ProxyID, proxy.Name, int(proxy.UserPort), int(proxy.ClientPort))

	if err := server.Start(); err != nil {
		return err
	}

	rm.servers[proxy.ProxyID] = server

	return nil
}

// StopServer 停止反向代理服务器
func (rm *ReverseProxyManager) StopServer(proxy *basic.Proxy) error {
	rm.mu.Lock()
	defer rm.mu.Unlock()

	if proxy.ProxyID == "" {
		return fmt.Errorf("ProxyID不能为空")
	}

	server, exists := rm.servers[proxy.ProxyID]
	if !exists {
		return fmt.Errorf("反向代理服务器不存在: %s", proxy.ProxyID)
	}

	if err := server.Stop(); err != nil {
		return err
	}

	delete(rm.servers, proxy.ProxyID)

	return nil
}