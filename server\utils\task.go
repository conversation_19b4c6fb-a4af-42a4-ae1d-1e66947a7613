package utils

import (
	"server/core/dbpool"
	"server/model/task"
	"time"

	"gorm.io/gorm"
)

func UpdateTaskStatus(taskID uint64, status, errorMsg string) {
	// 🚀 使用数据库连接池异步更新任务状态
	dbpool.ExecuteDBOperationAsyncAndWait("file_task_status_update", func(db *gorm.DB) error {
		updates := map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}

		if errorMsg != "" {
			updates["error"] = errorMsg
		}

		if status == "running" {
			now := time.Now()
			updates["started_at"] = &now
		} else if status == "completed" || status == "failed" || status == "cancelled" {
			now := time.Now()
			updates["completed_at"] = &now
		}

		return db.Model(&task.FileTransferTask{}).Where("id = ?", taskID).Updates(updates).Error
	})
}

// UpdateScreenshotTaskStatus 更新截图任务状态
func UpdateScreenshotTaskStatus(taskID uint64, status, errorMsg string) {
	// 🚀 使用数据库连接池异步更新任务状态
	dbpool.ExecuteDBOperationAsyncAndWait("screenshot_task_status_update", func(db *gorm.DB) error {
		updates := map[string]interface{}{
			"status":     status,
			"updated_at": time.Now(),
		}

		if errorMsg != "" {
			updates["error"] = errorMsg
		}

		if status == "running" {
			now := time.Now()
			updates["started_at"] = &now
		} else if status == "completed" || status == "failed" || status == "cancelled" {
			now := time.Now()
			updates["completed_at"] = &now
		}

		return db.Model(&task.ScreenshotTask{}).Where("id = ?", taskID).Updates(updates).Error
	})
}

// UpdateProcessTaskStatus 更新进程任务状态
func UpdateProcessTaskStatus(taskID uint64, status, errorMsg string) {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if errorMsg != "" {
		updates["error"] = errorMsg
	}

	if status == "running" {
		now := time.Now()
		updates["started_at"] = &now
	} else if status == "completed" || status == "failed" || status == "cancelled" {
		now := time.Now()
		updates["completed_at"] = &now
	}

	// 🚀 使用数据库连接池异步更新进程任务状态
	dbpool.ExecuteDBOperationAsyncAndWait("process_task_status_update", func(db *gorm.DB) error {
		return db.Model(&task.ProcessTask{}).Where("id = ?", taskID).Updates(updates).Error
	})
}

// UpdateTerminalTaskStatus 更新终端任务状态
func UpdateTerminalTaskStatus(taskID uint64, status, errorMsg string) {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if errorMsg != "" {
		updates["error"] = errorMsg
	}

	if status == "running" {
		now := time.Now()
		updates["started_at"] = &now
	} else if status == "completed" || status == "failed" || status == "cancelled" {
		now := time.Now()
		updates["completed_at"] = &now
	}

	// 🚀 使用数据库连接池异步更新终端任务状态
	dbpool.ExecuteDBOperationAsyncAndWait("terminal_task_status_update", func(db *gorm.DB) error {
		return db.Model(&task.TerminalTask{}).Where("id = ?", taskID).Updates(updates).Error
	})
}

// UpdateProxyTaskStatus 更新进程任务状态
func UpdateProxyTaskStatus(taskID uint64, status, errorMsg string) {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if errorMsg != "" {
		updates["error"] = errorMsg
	}

	if status == "running" {
		now := time.Now()
		updates["started_at"] = &now
	} else if status == "completed" || status == "failed" || status == "cancelled" {
		now := time.Now()
		updates["completed_at"] = &now
	}

	// 🚀 使用数据库连接池异步更新代理任务状态
	dbpool.ExecuteDBOperationAsyncAndWait("proxy_task_status_update", func(db *gorm.DB) error {
		return db.Model(&task.ProxyTask{}).Where("id = ?", taskID).Updates(updates).Error
	})
}

func UpdateNetworkTaskStatus(taskID uint64, status, errorMsg string) {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if errorMsg != "" {
		updates["error"] = errorMsg
	}

	if status == "running" {
		now := time.Now()
		updates["started_at"] = &now
	} else if status == "completed" || status == "failed" || status == "cancelled" {
		now := time.Now()
		updates["completed_at"] = &now
	}

	// 🚀 使用数据库连接池异步更新网络任务状态
	dbpool.ExecuteDBOperationAsyncAndWait("network_task_status_update", func(db *gorm.DB) error {
		return db.Model(&task.NetworkTask{}).Where("id = ?", taskID).Updates(updates).Error
	})
}

func UpdateTaskProgress(taskID uint64, progress int) {
	// 🚀 使用数据库连接池异步更新任务进度
	dbpool.ExecuteDBOperationAsyncAndWait("task_progress_update", func(db *gorm.DB) error {
		return db.Model(&task.FileTransferTask{}).
			Where("id = ?", taskID).
			Updates(map[string]interface{}{
				"progress":   progress,
				"updated_at": time.Now(),
			}).Error
	})
}

func UpdateTaskProgressWithSize(taskID uint64, progress int, transferredSize int64) {
	// 🚀 使用数据库连接池异步更新任务进度和大小
	dbpool.ExecuteDBOperationAsyncAndWait("task_progress_size_update", func(db *gorm.DB) error {
		return db.Model(&task.FileTransferTask{}).
			Where("id = ?", taskID).
			Updates(map[string]interface{}{
				"progress":         progress,
				"transferred_size": transferredSize,
				"updated_at":       time.Now(),
			}).Error
	})
}

// UpdateTaskProgressWithFileSize 更新任务进度、已传输大小和文件总大小
func UpdateTaskProgressWithFileSize(taskID uint64, progress int, transferredSize int64, fileSize int64) {
	// 🚀 使用数据库连接池异步更新任务进度和文件大小
	dbpool.ExecuteDBOperationAsyncAndWait("task_progress_filesize_update", func(db *gorm.DB) error {
		return db.Model(&task.FileTransferTask{}).
			Where("id = ?", taskID).
			Updates(map[string]interface{}{
				"progress":         progress,
				"transferred_size": transferredSize,
				"file_size":        fileSize,
				"updated_at":       time.Now(),
			}).Error
	})
}
