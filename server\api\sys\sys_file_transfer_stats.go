package sys

import (
	"server/core/filetransferpool"
	"server/global"
	"server/model/response"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FileTransferStatsApi struct{}

// GetFileTransferStats 获取文件传输统计信息
func (f *FileTransferStatsApi) GetFileTransferStats(ctx *gin.Context) {
	stats := filetransferpool.GetStats()
	
	// 计算额外的统计信息
	memoryUsage := filetransferpool.GetMemoryUsageEstimate()
	efficiencyRatio := filetransferpool.GetEfficiencyRatio()
	
	// 构建响应数据
	responseData := gin.H{
		"memory_pool_stats": stats,
		"memory_usage_bytes": memoryUsage,
		"memory_usage_mb": float64(memoryUsage) / (1024 * 1024),
		"efficiency_ratio": efficiencyRatio,
		"efficiency_percentage": efficiencyRatio * 100,
	}
	
	global.LOG.Info("获取文件传输统计信息",
		zap.Int64("total_bytes_transferred", stats.TotalBytesTransferred),
		zap.Int64("active_transfers", stats.ActiveTransfers),
		zap.Float64("efficiency_ratio", efficiencyRatio))
	
	response.OkWithData(responseData, ctx)
}

// ResetFileTransferStats 重置文件传输统计信息
func (f *FileTransferStatsApi) ResetFileTransferStats(ctx *gin.Context) {
	filetransferpool.ResetStats()
	
	global.LOG.Info("文件传输统计信息已重置")
	
	response.OkWithMessage("统计信息已重置", ctx)
}

// GetFileTransferPoolStatus 获取文件传输内存池状态
func (f *FileTransferStatsApi) GetFileTransferPoolStatus(ctx *gin.Context) {
	stats := filetransferpool.GetStats()
	
	// 计算各个池的使用情况
	poolStatus := gin.H{
		"chunk_pools": gin.H{
			"32kb": gin.H{
				"allocated": stats.Chunk32KAllocated,
				"reused": stats.Chunk32KReused,
				"efficiency": calculateEfficiency(stats.Chunk32KReused, stats.Chunk32KAllocated),
			},
			"64kb": gin.H{
				"allocated": stats.Chunk64KAllocated,
				"reused": stats.Chunk64KReused,
				"efficiency": calculateEfficiency(stats.Chunk64KReused, stats.Chunk64KAllocated),
			},
			"128kb": gin.H{
				"allocated": stats.Chunk128KAllocated,
				"reused": stats.Chunk128KReused,
				"efficiency": calculateEfficiency(stats.Chunk128KReused, stats.Chunk128KAllocated),
			},
			"256kb": gin.H{
				"allocated": stats.Chunk256KAllocated,
				"reused": stats.Chunk256KReused,
				"efficiency": calculateEfficiency(stats.Chunk256KReused, stats.Chunk256KAllocated),
			},
			"512kb": gin.H{
				"allocated": stats.Chunk512KAllocated,
				"reused": stats.Chunk512KReused,
				"efficiency": calculateEfficiency(stats.Chunk512KReused, stats.Chunk512KAllocated),
			},
			"1mb": gin.H{
				"allocated": stats.Chunk1MAllocated,
				"reused": stats.Chunk1MReused,
				"efficiency": calculateEfficiency(stats.Chunk1MReused, stats.Chunk1MAllocated),
			},
		},
		"overall": gin.H{
			"total_bytes_transferred": stats.TotalBytesTransferred,
			"total_bytes_transferred_mb": float64(stats.TotalBytesTransferred) / (1024 * 1024),
			"active_transfers": stats.ActiveTransfers,
			"memory_usage_estimate_mb": float64(filetransferpool.GetMemoryUsageEstimate()) / (1024 * 1024),
			"overall_efficiency": filetransferpool.GetEfficiencyRatio(),
			"last_reset_time": stats.LastResetTime,
		},
	}
	
	response.OkWithData(poolStatus, ctx)
}

// calculateEfficiency 计算效率比率
func calculateEfficiency(reused, allocated int64) float64 {
	if allocated == 0 {
		return 0.0
	}
	return float64(reused) / float64(allocated)
}
