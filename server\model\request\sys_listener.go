package request

// ListenerSearch 监听器搜索请求结构体
type ListenerSearch struct {
	Type              string `json:"type" form:"type"`
	Status            int    `json:"status" form:"status"`
	Remark            string `json:"remark" form:"remark"`
	LocalListenAddr   string `json:"localListenAddr" form:"localListenAddr"`
	RemoteConnectAddr string `json:"remoteConnectAddr" form:"remoteConnectAddr"`
	PageInfo
}

// ListenerStatusChange 监听器状态变更请求结构体
type ListenerStatusChange struct {
	ID     uint `json:"id" form:"id"`
	Status int  `json:"status" form:"status"`
}
