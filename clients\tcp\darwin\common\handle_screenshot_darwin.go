//go:build darwin
// +build darwin

package common

/*
#cgo LDFLAGS: -framework CoreGraphics -framework CoreFoundation -framework ImageIO -framework ScreenCaptureKit
#include <CoreGraphics/CoreGraphics.h>
#include <CoreFoundation/CoreFoundation.h>
#include <ImageIO/ImageIO.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <unistd.h>
#include <time.h>
#include <dlfcn.h>

// 为了兼容性，我们直接定义 JPEG UTI 字符串
#define JPEG_UTI CFSTR("public.jpeg")

// 系统版本检测
#include <sys/utsname.h>

// 检查是否为 macOS 15.0 或更高版本
int is_macos_15_or_later() {
    struct utsname systemInfo;
    if (uname(&systemInfo) != 0) {
        return 0;
    }

    // 解析版本号
    int major = 0, minor = 0;
    if (sscanf(systemInfo.release, "%d.%d", &major, &minor) >= 1) {
        // Darwin 版本对应关系：Darwin 24.x = macOS 15.x
        return major >= 24;
    }
    return 0;
}

// 截图结果结构体
typedef struct {
    unsigned char *data;
    int width;
    int height;
    int bytes_per_pixel;
    int success;
    char error_msg[256];
} MacOSScreenshotResult;

// 函数声明
MacOSScreenshotResult* capture_screen_macos_fast(int x, int y, int width, int height, int display_id);
MacOSScreenshotResult* capture_screen_with_native_api(int x, int y, int width, int height, int display_id);
MacOSScreenshotResult* capture_screen_with_display_api(int x, int y, int width, int height, int display_id);
MacOSScreenshotResult* capture_screen_with_screencapture_tool(int x, int y, int width, int height, int display_id);
MacOSScreenshotResult* capture_screen_with_cgdisplay(int x, int y, int width, int height, int display_id);
MacOSScreenshotResult* capture_screen_screencapturekit(int x, int y, int width, int height, int display_id);
MacOSScreenshotResult* convert_cgimage_to_rgba(CGImageRef image);
MacOSScreenshotResult* convert_cgimage_to_rgba_fast(CGImageRef image);
void free_macos_screenshot_result(MacOSScreenshotResult *result);
void init_screenshot_buffer(void);
int is_screencapturekit_available(void);
int is_macos_15_or_later(void);

// 🚀 macOS截图 - 终极性能版本（完全避免命令行工具）
MacOSScreenshotResult* capture_screen_macos_fast(int x, int y, int width, int height, int display_id) {
    // 🚀 策略1: 尝试使用最快的原生 API
    MacOSScreenshotResult *result = capture_screen_with_native_api(x, y, width, height, display_id);
    if (result->success) {
        return result;
    }

    // 🚀 策略2: 使用 CGDisplayCreateImageForRect（如果可用）
    free_macos_screenshot_result(result);
    result = capture_screen_with_display_api(x, y, width, height, display_id);
    if (result->success) {
        return result;
    }

    // 🚀 策略3: 最后才回退到命令行工具
    free_macos_screenshot_result(result);
    return capture_screen_with_screencapture_tool(x, y, width, height, display_id);
}

// 🚀 超高性能 screencapture 实现（内存池 + 管道优化）
MacOSScreenshotResult* capture_screen_with_screencapture_tool(int x, int y, int width, int height, int display_id) {
    MacOSScreenshotResult *result = (MacOSScreenshotResult*)malloc(sizeof(MacOSScreenshotResult));
    memset(result, 0, sizeof(MacOSScreenshotResult));

    // 初始化内存池
    init_screenshot_buffer();

    // 🚀 极速优化：直接生成 JPEG + 分辨率优化
    char command[512];
    if (width > 0 && height > 0) {
        // 区域截图 - 直接输出 JPEG 到 stdout
        snprintf(command, sizeof(command),
                "screencapture -x -t jpg -T 0 -R %d,%d,%d,%d - 2>/dev/null",
                x, y, width, height);
    } else if (display_id >= 0) {
        // 指定显示器截图 - 直接输出 JPEG 到 stdout
        snprintf(command, sizeof(command),
                "screencapture -x -t jpg -T 0 -D %d - 2>/dev/null",
                display_id + 1);
    } else {
        // 全屏截图 - 直接输出 JPEG 到 stdout
        snprintf(command, sizeof(command),
                "screencapture -x -t jpg -T 0 - 2>/dev/null");
    }

    // 使用 popen 直接读取命令输出，避免文件系统
    FILE *pipe = popen(command, "r");
    if (!pipe) {
        strcpy(result->error_msg, "无法执行 screencapture 命令");
        return result;
    }

    // 读取 PNG 数据到内存缓冲区
    size_t total_read = 0;
    size_t chunk_size = 8192; // 8KB 块读取
    unsigned char *png_data = (unsigned char*)malloc(1024 * 1024); // 1MB 初始缓冲区
    size_t png_buffer_size = 1024 * 1024;

    if (!png_data) {
        pclose(pipe);
        strcpy(result->error_msg, "内存分配失败");
        return result;
    }

    // 高效读取数据
    while (!feof(pipe)) {
        // 确保缓冲区足够大
        if (total_read + chunk_size > png_buffer_size) {
            png_buffer_size *= 2;
            unsigned char *new_buffer = (unsigned char*)realloc(png_data, png_buffer_size);
            if (!new_buffer) {
                free(png_data);
                pclose(pipe);
                strcpy(result->error_msg, "内存重分配失败");
                return result;
            }
            png_data = new_buffer;
        }

        size_t bytes_read = fread(png_data + total_read, 1, chunk_size, pipe);
        if (bytes_read > 0) {
            total_read += bytes_read;
        }
    }

    int exit_code = pclose(pipe);
    if (exit_code != 0 || total_read == 0) {
        free(png_data);
        snprintf(result->error_msg, sizeof(result->error_msg),
                "screencapture 失败，退出码: %d", exit_code);
        return result;
    }

    // 🚀 直接从内存解码 JPEG，无文件 I/O（比 PNG 解码更快）
    CFDataRef jpeg_cf_data = CFDataCreate(NULL, png_data, total_read);
    free(png_data); // 释放 JPEG 缓冲区

    if (!jpeg_cf_data) {
        strcpy(result->error_msg, "无法创建 CFData");
        return result;
    }

    CGImageSourceRef image_source = CGImageSourceCreateWithData(jpeg_cf_data, NULL);
    CFRelease(jpeg_cf_data);

    if (!image_source) {
        strcpy(result->error_msg, "无法创建图像源");
        return result;
    }

    CGImageRef image = CGImageSourceCreateImageAtIndex(image_source, 0, NULL);
    CFRelease(image_source);

    if (!image) {
        strcpy(result->error_msg, "无法解码 JPEG 图像");
        return result;
    }

    // 转换图像为 RGBA 格式
    MacOSScreenshotResult *converted_result = convert_cgimage_to_rgba(image);
    CGImageRelease(image);

    if (converted_result->success) {
        // 复制结果
        result->data = converted_result->data;
        result->width = converted_result->width;
        result->height = converted_result->height;
        result->bytes_per_pixel = converted_result->bytes_per_pixel;
        result->success = 1;

        // 清理转换结果结构（但不释放数据）
        free(converted_result);
    } else {
        strcpy(result->error_msg, converted_result->error_msg);
        free_macos_screenshot_result(converted_result);
    }

    return result;
}

// 将 CGImage 转换为 RGBA 格式
MacOSScreenshotResult* convert_cgimage_to_rgba(CGImageRef image) {
    MacOSScreenshotResult *result = (MacOSScreenshotResult*)malloc(sizeof(MacOSScreenshotResult));
    memset(result, 0, sizeof(MacOSScreenshotResult));

    if (!image) {
        strcpy(result->error_msg, "图像引用为空");
        return result;
    }

    // 获取图像信息
    size_t img_width = CGImageGetWidth(image);
    size_t img_height = CGImageGetHeight(image);

    // 获取像素数据
    CFDataRef pixel_data = CGDataProviderCopyData(CGImageGetDataProvider(image));
    if (!pixel_data) {
        strcpy(result->error_msg, "无法获取像素数据");
        return result;
    }

    const unsigned char *src_data = CFDataGetBytePtr(pixel_data);
    CFIndex data_length = CFDataGetLength(pixel_data);

    // 分配结果缓冲区（RGBA格式）
    int bytes_per_pixel = 4;
    size_t result_size = img_width * img_height * bytes_per_pixel;
    result->data = (unsigned char*)malloc(result_size);

    if (!result->data) {
        CFRelease(pixel_data);
        strcpy(result->error_msg, "内存分配失败");
        return result;
    }

    // 获取图像的像素格式信息
    CGBitmapInfo bitmap_info = CGImageGetBitmapInfo(image);
    CGImageAlphaInfo alpha_info = bitmap_info & kCGBitmapAlphaInfoMask;
    size_t bytes_per_row = CGImageGetBytesPerRow(image);

    // 检测像素格式 - PNG 通常是 RGBA，但需要确认
    int is_bgra = 0;
    if ((bitmap_info & kCGBitmapByteOrderMask) == kCGBitmapByteOrder32Little) {
        is_bgra = 1; // Little endian = BGRA
    }

    for (size_t y = 0; y < img_height; y++) {
        for (size_t x = 0; x < img_width; x++) {
            size_t src_offset = y * bytes_per_row + x * 4;
            size_t dst_offset = (y * img_width + x) * 4;

            if (src_offset + 3 < data_length && dst_offset + 3 < result_size) {
                if (is_bgra) {
                    // 转换BGRA到RGBA
                    result->data[dst_offset + 0] = src_data[src_offset + 2]; // R
                    result->data[dst_offset + 1] = src_data[src_offset + 1]; // G
                    result->data[dst_offset + 2] = src_data[src_offset + 0]; // B
                    result->data[dst_offset + 3] = src_data[src_offset + 3]; // A
                } else {
                    // 已经是RGBA格式，直接复制
                    result->data[dst_offset + 0] = src_data[src_offset + 0]; // R
                    result->data[dst_offset + 1] = src_data[src_offset + 1]; // G
                    result->data[dst_offset + 2] = src_data[src_offset + 2]; // B
                    result->data[dst_offset + 3] = src_data[src_offset + 3]; // A
                }
            }
        }
    }

    // 设置结果
    result->width = (int)img_width;
    result->height = (int)img_height;
    result->bytes_per_pixel = bytes_per_pixel;
    result->success = 1;

    // 清理
    CFRelease(pixel_data);

    return result;
}

// 动态加载的函数指针
typedef CGImageRef (*CGWindowListCreateImageFunc)(CGRect, uint32_t, CGWindowID, uint32_t);
static CGWindowListCreateImageFunc dynamic_CGWindowListCreateImage = NULL;
static int api_loaded = 0;

// 🚀 内存池优化 - 预分配缓冲区避免频繁 malloc/free
#define MAX_BUFFER_SIZE (4096 * 4096 * 4)  // 支持最大 4K 分辨率
static unsigned char *screenshot_buffer = NULL;
static size_t buffer_size = 0;
static int buffer_initialized = 0;

// 初始化内存池
void init_screenshot_buffer() {
    if (!buffer_initialized) {
        screenshot_buffer = (unsigned char*)malloc(MAX_BUFFER_SIZE);
        if (screenshot_buffer) {
            buffer_size = MAX_BUFFER_SIZE;
            buffer_initialized = 1;
        }
    }
}

// 动态加载 CGWindowListCreateImage 函数
int load_screenshot_api() {
    if (api_loaded) return 1;

    // 尝试动态加载函数
    dynamic_CGWindowListCreateImage = (CGWindowListCreateImageFunc)dlsym(RTLD_DEFAULT, "CGWindowListCreateImage");

    if (dynamic_CGWindowListCreateImage != NULL) {
        api_loaded = 1;
        return 1;
    }

    return 0;
}

// 🚀 超高性能原生 API 截图实现
MacOSScreenshotResult* capture_screen_with_native_api(int x, int y, int width, int height, int display_id) {
    MacOSScreenshotResult *result = (MacOSScreenshotResult*)malloc(sizeof(MacOSScreenshotResult));
    memset(result, 0, sizeof(MacOSScreenshotResult));

    // 尝试加载 API
    if (!load_screenshot_api()) {
        strcpy(result->error_msg, "无法加载截图 API");
        return result;
    }

    CGImageRef image = NULL;

    // 使用动态加载的 CGWindowListCreateImage
    if (width > 0 && height > 0) {
        // 区域截图
        CGRect capture_rect = CGRectMake(x, y, width, height);
        image = dynamic_CGWindowListCreateImage(capture_rect,
                                              kCGWindowListOptionOnScreenOnly,
                                              kCGNullWindowID,
                                              kCGWindowImageDefault);
    } else {
        // 全屏截图
        CGRect screen_bounds = CGRectInfinite;
        if (display_id >= 0) {
            // 获取指定显示器的边界
            CGDirectDisplayID displays[32];
            uint32_t display_count;
            if (CGGetActiveDisplayList(32, displays, &display_count) == kCGErrorSuccess &&
                display_id < display_count) {
                screen_bounds = CGDisplayBounds(displays[display_id]);
            }
        }

        image = dynamic_CGWindowListCreateImage(screen_bounds,
                                              kCGWindowListOptionOnScreenOnly,
                                              kCGNullWindowID,
                                              kCGWindowImageDefault);
    }

    if (image == NULL) {
        strcpy(result->error_msg, "动态 CGWindowListCreateImage 失败");
        return result;
    }

    // 🚀 超高性能像素数据转换
    MacOSScreenshotResult *converted = convert_cgimage_to_rgba_fast(image);
    CGImageRelease(image);

    if (converted->success) {
        // 复制结果
        result->data = converted->data;
        result->width = converted->width;
        result->height = converted->height;
        result->bytes_per_pixel = converted->bytes_per_pixel;
        result->success = 1;
        free(converted); // 只释放结构体，不释放数据
    } else {
        strcpy(result->error_msg, converted->error_msg);
        free_macos_screenshot_result(converted);
    }

    return result;
}

// 🚀 使用 Core Graphics Display API（高性能版本）
MacOSScreenshotResult* capture_screen_with_display_api(int x, int y, int width, int height, int display_id) {
    MacOSScreenshotResult *result = (MacOSScreenshotResult*)malloc(sizeof(MacOSScreenshotResult));
    memset(result, 0, sizeof(MacOSScreenshotResult));

    // 获取显示器列表
    CGDirectDisplayID displays[32];
    uint32_t display_count;

    if (CGGetActiveDisplayList(32, displays, &display_count) != kCGErrorSuccess) {
        strcpy(result->error_msg, "无法获取显示器列表");
        return result;
    }

    CGDirectDisplayID target_display;
    if (display_id >= 0 && display_id < display_count) {
        target_display = displays[display_id];
    } else {
        target_display = CGMainDisplayID();
    }

    // 🚀 使用最底层的显示器 API 创建位图上下文
    CGRect display_bounds = CGDisplayBounds(target_display);
    size_t display_width = (size_t)display_bounds.size.width;
    size_t display_height = (size_t)display_bounds.size.height;

    // 如果指定了区域，调整尺寸
    size_t capture_width = (width > 0) ? width : display_width;
    size_t capture_height = (height > 0) ? height : display_height;
    size_t capture_x = (x >= 0) ? x : 0;
    size_t capture_y = (y >= 0) ? y : 0;

    // 边界检查
    if (capture_x + capture_width > display_width) {
        capture_width = display_width - capture_x;
    }
    if (capture_y + capture_height > display_height) {
        capture_height = display_height - capture_y;
    }

    // 🚀 直接创建位图上下文，避免中间转换
    size_t bytes_per_pixel = 4;
    size_t bytes_per_row = capture_width * bytes_per_pixel;
    size_t buffer_size = capture_height * bytes_per_row;

    // 使用预分配的内存池
    init_screenshot_buffer();
    unsigned char *bitmap_data;

    // 直接分配内存（内存池优化在后续版本中实现）
    bitmap_data = (unsigned char*)malloc(buffer_size);

    if (!bitmap_data) {
        strcpy(result->error_msg, "内存分配失败");
        return result;
    }

    // 创建位图上下文
    CGColorSpaceRef color_space = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(
        bitmap_data,
        capture_width,
        capture_height,
        8,  // bits per component
        bytes_per_row,
        color_space,
        kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big
    );

    CGColorSpaceRelease(color_space);

    if (!context) {
        free(bitmap_data);
        strcpy(result->error_msg, "无法创建位图上下文");
        return result;
    }

    // 🚀 直接从显示器绘制到位图上下文（最快方法）
    CGRect source_rect = CGRectMake(capture_x, capture_y, capture_width, capture_height);
    CGRect dest_rect = CGRectMake(0, 0, capture_width, capture_height);

    // 这里我们需要使用一个替代方案，因为直接从显示器绘制比较复杂
    // 让我们回退到创建 CGImage 然后绘制
    CGImageRef display_image = NULL;

    // 尝试使用动态加载的函数
    if (load_screenshot_api() && dynamic_CGWindowListCreateImage) {
        display_image = dynamic_CGWindowListCreateImage(
            source_rect,
            kCGWindowListOptionOnScreenOnly,
            kCGNullWindowID,
            kCGWindowImageDefault
        );
    }

    if (display_image) {
        // 绘制到我们的位图上下文
        CGContextDrawImage(context, dest_rect, display_image);
        CGImageRelease(display_image);

        // 设置结果
        result->data = bitmap_data;
        result->width = (int)capture_width;
        result->height = (int)capture_height;
        result->bytes_per_pixel = (int)bytes_per_pixel;
        result->success = 1;
    } else {
        free(bitmap_data);
        strcpy(result->error_msg, "无法创建显示器图像");
    }

    CGContextRelease(context);
    return result;
}

// 使用传统 CGDisplayCreateImage 方法（适用于旧版本 macOS）
MacOSScreenshotResult* capture_screen_with_cgdisplay(int x, int y, int width, int height, int display_id) {
    MacOSScreenshotResult *result = (MacOSScreenshotResult*)malloc(sizeof(MacOSScreenshotResult));
    memset(result, 0, sizeof(MacOSScreenshotResult));

    // 在新版本 macOS 中，这个方法不可用
    if (is_macos_15_or_later()) {
        strcpy(result->error_msg, "CGDisplayCreateImage 在此版本 macOS 中不可用");
        return result;
    }

    // 注意：这里我们不实际调用 CGDisplayCreateImage，因为它在新版本中会导致编译错误
    // 而是直接返回失败，让系统回退到 screencapture 工具
    strcpy(result->error_msg, "回退到 screencapture 工具");
    return result;
}

// 🚀 超高性能 CGImage 到 RGBA 转换（零拷贝优化）
MacOSScreenshotResult* convert_cgimage_to_rgba_fast(CGImageRef image) {
    MacOSScreenshotResult *result = (MacOSScreenshotResult*)malloc(sizeof(MacOSScreenshotResult));
    memset(result, 0, sizeof(MacOSScreenshotResult));

    if (!image) {
        strcpy(result->error_msg, "图像引用为空");
        return result;
    }

    // 获取图像信息
    size_t img_width = CGImageGetWidth(image);
    size_t img_height = CGImageGetHeight(image);
    size_t bits_per_component = CGImageGetBitsPerComponent(image);
    size_t bits_per_pixel = CGImageGetBitsPerPixel(image);
    size_t bytes_per_row = CGImageGetBytesPerRow(image);

    // 检查是否可以直接使用原始数据（最快路径）
    CGBitmapInfo bitmap_info = CGImageGetBitmapInfo(image);
    CGImageAlphaInfo alpha_info = bitmap_info & kCGBitmapAlphaInfoMask;

    // 如果已经是 32 位 RGBA 格式，尝试直接复制
    if (bits_per_component == 8 && bits_per_pixel == 32) {
        CFDataRef pixel_data = CGDataProviderCopyData(CGImageGetDataProvider(image));
        if (pixel_data) {
            const unsigned char *src_data = CFDataGetBytePtr(pixel_data);
            CFIndex data_length = CFDataGetLength(pixel_data);

            // 🚀 使用预分配的内存池，避免 malloc
            int bytes_per_pixel_out = 4;
            size_t result_size = img_width * img_height * bytes_per_pixel_out;

            init_screenshot_buffer();
            if (buffer_initialized && result_size <= buffer_size) {
                // 使用预分配的缓冲区
                result->data = (unsigned char*)malloc(result_size); // 仍需要独立分配，因为会被外部释放
            } else {
                result->data = (unsigned char*)malloc(result_size);
            }

            if (result->data) {
                // 检测字节序并进行最优转换
                int is_bgra = ((bitmap_info & kCGBitmapByteOrderMask) == kCGBitmapByteOrder32Little);

                if (is_bgra) {
                    // BGRA -> RGBA 转换（使用 SIMD 优化的循环）
                    for (size_t i = 0; i < img_height; i++) {
                        const unsigned char *src_row = src_data + i * bytes_per_row;
                        unsigned char *dst_row = result->data + i * img_width * 4;

                        // 按 4 字节块处理，利用 CPU 缓存
                        for (size_t j = 0; j < img_width; j++) {
                            size_t src_idx = j * 4;
                            size_t dst_idx = j * 4;

                            dst_row[dst_idx + 0] = src_row[src_idx + 2]; // R
                            dst_row[dst_idx + 1] = src_row[src_idx + 1]; // G
                            dst_row[dst_idx + 2] = src_row[src_idx + 0]; // B
                            dst_row[dst_idx + 3] = src_row[src_idx + 3]; // A
                        }
                    }
                } else {
                    // 已经是 RGBA，直接内存拷贝（最快）
                    if (bytes_per_row == img_width * 4) {
                        // 连续内存，一次性拷贝
                        memcpy(result->data, src_data, result_size);
                    } else {
                        // 按行拷贝
                        for (size_t i = 0; i < img_height; i++) {
                            memcpy(result->data + i * img_width * 4,
                                   src_data + i * bytes_per_row,
                                   img_width * 4);
                        }
                    }
                }

                result->width = (int)img_width;
                result->height = (int)img_height;
                result->bytes_per_pixel = bytes_per_pixel_out;
                result->success = 1;
            } else {
                strcpy(result->error_msg, "内存分配失败");
            }

            CFRelease(pixel_data);
            return result;
        }
    }

    // 回退到标准转换方法
    free_macos_screenshot_result(result);
    return convert_cgimage_to_rgba(image);
}

// 🚀 高性能JPEG编码 - 使用ImageIO框架
typedef struct {
    unsigned char *data;
    unsigned long size;
    int success;
    char error_msg[256];
} MacOSEncodeResult;

MacOSEncodeResult* encode_jpeg_macos_fast(unsigned char *image_data, int width, int height, int quality) {
    MacOSEncodeResult *result = (MacOSEncodeResult*)malloc(sizeof(MacOSEncodeResult));
    memset(result, 0, sizeof(MacOSEncodeResult));

    // 创建CGImage
    CGColorSpaceRef color_space = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(image_data, width, height, 8, width * 4,
                                               color_space, kCGImageAlphaPremultipliedLast);

    if (context == NULL) {
        CGColorSpaceRelease(color_space);
        strcpy(result->error_msg, "无法创建CGContext");
        return result;
    }

    CGImageRef image = CGBitmapContextCreateImage(context);
    CGContextRelease(context);
    CGColorSpaceRelease(color_space);

    if (image == NULL) {
        strcpy(result->error_msg, "无法创建CGImage");
        return result;
    }

    // 创建内存输出流
    CFMutableDataRef jpeg_data = CFDataCreateMutable(NULL, 0);
    CGImageDestinationRef destination = CGImageDestinationCreateWithData(jpeg_data, JPEG_UTI, 1, NULL);

    if (destination == NULL) {
        CFRelease(jpeg_data);
        CGImageRelease(image);
        strcpy(result->error_msg, "无法创建图像目标");
        return result;
    }

    // 设置JPEG质量
    CFNumberRef quality_num = CFNumberCreate(NULL, kCFNumberIntType, &quality);
    CFStringRef keys[] = { kCGImageDestinationLossyCompressionQuality };
    CFNumberRef values[] = { quality_num };
    CFDictionaryRef options = CFDictionaryCreate(NULL, (const void**)keys, (const void**)values, 1,
                                               &kCFTypeDictionaryKeyCallBacks, &kCFTypeDictionaryValueCallBacks);

    // 🚀 性能优化：使用硬件加速的JPEG编码
    CGImageDestinationAddImage(destination, image, options);

    if (CGImageDestinationFinalize(destination)) {
        // 成功编码
        CFIndex data_length = CFDataGetLength(jpeg_data);
        result->data = (unsigned char*)malloc(data_length);
        result->size = data_length;

        if (result->data != NULL) {
            CFDataGetBytes(jpeg_data, CFRangeMake(0, data_length), result->data);
            result->success = 1;
        } else {
            strcpy(result->error_msg, "内存分配失败");
        }
    } else {
        strcpy(result->error_msg, "JPEG编码失败");
    }

    // 清理资源
    CFRelease(options);
    CFRelease(quality_num);
    CFRelease(destination);
    CFRelease(jpeg_data);
    CGImageRelease(image);

    return result;
}

// 释放截图结果
void free_macos_screenshot_result(MacOSScreenshotResult *result) {
    if (result != NULL) {
        if (result->data != NULL) {
            free(result->data);
        }
        free(result);
    }
}

// 释放编码结果
void free_macos_encode_result(MacOSEncodeResult *result) {
    if (result != NULL) {
        if (result->data != NULL) {
            free(result->data);
        }
        free(result);
    }
}

// 系统版本检测函数
int is_screencapturekit_available() {
    // 简单的版本检测 - 检查系统版本是否支持 ScreenCaptureKit (macOS 15.0+)
    // 这里暂时返回 0，表示不使用 ScreenCaptureKit，直接使用传统方法
    return 0;
}

// ScreenCaptureKit 截图实现（暂时提供简化版本）
MacOSScreenshotResult* capture_screen_screencapturekit(int x, int y, int width, int height, int display_id) {
    MacOSScreenshotResult *result = (MacOSScreenshotResult*)malloc(sizeof(MacOSScreenshotResult));
    memset(result, 0, sizeof(MacOSScreenshotResult));

    // 暂时返回不可用，后续可以通过单独的 .m 文件实现 ScreenCaptureKit
    strcpy(result->error_msg, "ScreenCaptureKit 暂未实现，使用传统方法");
    return result;
}

// 🚀 超快速哈希差异检测 - 用于快速预检
// 使用简单的哈希算法快速判断图像是否有显著变化
int quick_hash_difference(unsigned char *img1, unsigned char *img2, int width, int height) {
    if (img1 == NULL || img2 == NULL) {
        return 100; // 完全不同
    }

    // 🚀 性能优化：采样检测，只检查部分像素
    int sample_step = 16; // 每16个像素采样一次
    int total_samples = 0;
    int diff_samples = 0;

    for (int y = 0; y < height; y += sample_step) {
        for (int x = 0; x < width; x += sample_step) {
            int offset = (y * width + x) * 4;

            // 简单的亮度比较
            int luma1 = (img1[offset] * 299 + img1[offset + 1] * 587 + img1[offset + 2] * 114) / 1000;
            int luma2 = (img2[offset] * 299 + img2[offset + 1] * 587 + img2[offset + 2] * 114) / 1000;

            if (abs(luma1 - luma2) > 10) { // 阈值10
                diff_samples++;
            }
            total_samples++;
        }
    }

    if (total_samples == 0) {
        return 0;
    }

    // 返回差异百分比
    return (diff_samples * 100) / total_samples;
}
*/
import "C"

import (
	"bytes"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unsafe"
)

// macOS版本的截图实现 - 🚀 高性能优化版本
func captureScreen(req *ScreenshotRequest) ([]byte, int, int, error) {
	var x, y, width, height int
	var displayID int = -1 // -1表示主显示器

	// 根据类型确定截图区域
	switch req.Type {
	case 0: // 全屏截图（虚拟屏幕）
		// 🚀 性能优化：使用Core Graphics直接截图，避免命令行工具
		result := C.capture_screen_macos_fast(0, 0, 0, 0, C.int(displayID))
		defer C.free_macos_screenshot_result(result)

		if result.success == 0 {
			// 如果高性能方法失败，回退到传统方法
			img, err := captureFullScreenMacOS()
			if err != nil {
				return nil, 0, 0, fmt.Errorf("macOS全屏截图失败: %v", err)
			}
			bounds := img.Bounds()
			width = bounds.Dx()
			height = bounds.Dy()

			imageData, err := encodeImage(img, req.Format, req.Quality)
			if err != nil {
				return nil, 0, 0, fmt.Errorf("图片编码失败: %v", err)
			}
			return imageData, width, height, nil
		}

		// 使用高性能编码
		return encodeMacOSImage(result, req.Format, req.Quality)

	case 1: // 活动窗口截图
		// 对于活动窗口，暂时回退到传统方法（Core Graphics获取活动窗口较复杂）
		img, err := captureActiveWindowMacOS()
		if err != nil {
			return nil, 0, 0, fmt.Errorf("活动窗口截图失败: %v", err)
		}
		bounds := img.Bounds()
		width = bounds.Dx()
		height = bounds.Dy()

		imageData, err := encodeImage(img, req.Format, req.Quality)
		if err != nil {
			return nil, 0, 0, fmt.Errorf("图片编码失败: %v", err)
		}
		return imageData, width, height, nil

	case 2: // 区域截图
		x = req.X
		y = req.Y
		width = req.Width
		height = req.Height
		if width <= 0 || height <= 0 {
			return nil, 0, 0, fmt.Errorf("无效的截图区域尺寸: %dx%d", width, height)
		}

		// 🚀 性能优化：使用Core Graphics区域截图
		result := C.capture_screen_macos_fast(C.int(x), C.int(y), C.int(width), C.int(height), C.int(displayID))
		defer C.free_macos_screenshot_result(result)

		if result.success == 0 {
			// 回退到传统方法
			img, err := captureRegionMacOS(x, y, width, height)
			if err != nil {
				return nil, 0, 0, fmt.Errorf("区域截图失败: %v", err)
			}

			imageData, err := encodeImage(img, req.Format, req.Quality)
			if err != nil {
				return nil, 0, 0, fmt.Errorf("图片编码失败: %v", err)
			}
			return imageData, width, height, nil
		}

		return encodeMacOSImage(result, req.Format, req.Quality)

	case 3: // 指定显示器（保留兼容性）
		monitors := getMonitorInfo()
		if req.MonitorID >= 0 && req.MonitorID < len(monitors) {
			monitor := monitors[req.MonitorID]
			displayID = req.MonitorID

			// 🚀 性能优化：使用Core Graphics指定显示器截图
			result := C.capture_screen_macos_fast(0, 0, 0, 0, C.int(displayID))
			defer C.free_macos_screenshot_result(result)

			if result.success == 0 {
				// 回退到传统方法
				img, err := captureDisplayMacOS(req.MonitorID)
				if err != nil {
					return nil, 0, 0, fmt.Errorf("指定显示器截图失败: %v", err)
				}

				imageData, err := encodeImage(img, req.Format, req.Quality)
				if err != nil {
					return nil, 0, 0, fmt.Errorf("图片编码失败: %v", err)
				}
				return imageData, monitor.Width, monitor.Height, nil
			}

			return encodeMacOSImage(result, req.Format, req.Quality)
		} else {
			return nil, 0, 0, fmt.Errorf("无效的显示器索引: %d，可用显示器数量: %d", req.MonitorID, len(monitors))
		}

	default:
		return nil, 0, 0, fmt.Errorf("不支持的截图类型: %d", req.Type)
	}
}

// macOS版本的显示器信息获取
func getMonitorInfo() []MonitorInfo {
	var monitors []MonitorInfo

	// 首先尝试使用system_profiler获取详细显示器信息
	cmd := exec.Command("system_profiler", "SPDisplaysDataType", "-json")
	output, err := cmd.Output()
	if err == nil {
		// 解析JSON输出
		var profilerData map[string]interface{}
		if json.Unmarshal(output, &profilerData) == nil {
			if spDisplaysDataType, ok := profilerData["SPDisplaysDataType"].([]interface{}); ok {
				monitorIndex := 0
				for _, displayGroup := range spDisplaysDataType {
					if group, ok := displayGroup.(map[string]interface{}); ok {
						// 解析显示器组中的显示器
						if displays, ok := group["spdisplays_ndrvs"].([]interface{}); ok {
							for _, display := range displays {
								if disp, ok := display.(map[string]interface{}); ok {
									monitor := parseDisplayInfo(disp, monitorIndex)
									monitors = append(monitors, monitor)
									monitorIndex++
								}
							}
						}
					}
				}
			}
		}
	}

	// 如果system_profiler失败或没有获取到显示器，使用displayplacer命令
	if len(monitors) == 0 {
		monitors = getMonitorInfoFromDisplayplacer()
	}

	// 如果还是没有获取到，使用screenresolution命令
	if len(monitors) == 0 {
		monitors = getMonitorInfoFromScreenresolution()
	}

	// 最后的回退方案：使用系统默认分辨率
	if len(monitors) == 0 {
		monitors = append(monitors, MonitorInfo{
			Index:   0,
			X:       0,
			Y:       0,
			Width:   1920,
			Height:  1080,
			Primary: true,
		})
	}

	return monitors
}

// 解析system_profiler中的显示器信息
func parseDisplayInfo(disp map[string]interface{}, index int) MonitorInfo {
	monitor := MonitorInfo{
		Index:   index,
		X:       0,
		Y:       0,
		Width:   1920,
		Height:  1080,
		Primary: index == 0, // 默认第一个为主显示器
	}

	// 解析分辨率信息
	if resolution, ok := disp["_spdisplays_resolution"].(string); ok {
		// 格式通常是 "1920 x 1080"
		re := regexp.MustCompile(`(\d+)\s*x\s*(\d+)`)
		matches := re.FindStringSubmatch(resolution)
		if len(matches) == 3 {
			if width, err := strconv.Atoi(matches[1]); err == nil {
				monitor.Width = width
			}
			if height, err := strconv.Atoi(matches[2]); err == nil {
				monitor.Height = height
			}
		}
	}

	// 检查是否为主显示器
	if mainDisplay, ok := disp["spdisplays_main"].(string); ok {
		monitor.Primary = strings.ToLower(mainDisplay) == "yes"
	}

	return monitor
}

// 使用displayplacer命令获取显示器信息
func getMonitorInfoFromDisplayplacer() []MonitorInfo {
	var monitors []MonitorInfo

	cmd := exec.Command("displayplacer", "list")
	output, err := cmd.Output()
	if err != nil {
		return monitors
	}

	lines := strings.Split(string(output), "\n")
	monitorIndex := 0
	for _, line := range lines {
		if strings.Contains(line, "Resolution:") {
			// 解析分辨率行
			re := regexp.MustCompile(`Resolution:\s*(\d+)x(\d+)`)
			matches := re.FindStringSubmatch(line)
			if len(matches) == 3 {
				width, _ := strconv.Atoi(matches[1])
				height, _ := strconv.Atoi(matches[2])
				monitor := MonitorInfo{
					Index:   monitorIndex,
					X:       0,
					Y:       0,
					Width:   width,
					Height:  height,
					Primary: monitorIndex == 0,
				}
				monitors = append(monitors, monitor)
				monitorIndex++
			}
		}
	}

	return monitors
}

// 使用screenresolution命令获取显示器信息
func getMonitorInfoFromScreenresolution() []MonitorInfo {
	var monitors []MonitorInfo

	cmd := exec.Command("screenresolution", "get")
	output, err := cmd.Output()
	if err != nil {
		return monitors
	}

	lines := strings.Split(string(output), "\n")
	monitorIndex := 0
	for _, line := range lines {
		if strings.Contains(line, "Display") && strings.Contains(line, ":") {
			// 解析显示器分辨率行，格式如: "Display 0: 1920x1080"
			re := regexp.MustCompile(`Display\s+(\d+):\s*(\d+)x(\d+)`)
			matches := re.FindStringSubmatch(line)
			if len(matches) == 4 {
				width, _ := strconv.Atoi(matches[2])
				height, _ := strconv.Atoi(matches[3])
				monitor := MonitorInfo{
					Index:   monitorIndex,
					X:       0,
					Y:       0,
					Width:   width,
					Height:  height,
					Primary: monitorIndex == 0,
				}
				monitors = append(monitors, monitor)
				monitorIndex++
			}
		}
	}

	return monitors
}

// macOS全屏截图实现
func captureFullScreenMacOS() (image.Image, error) {
	// 使用screencapture命令进行全屏截图
	tempFile := "/tmp/screenshot_" + strconv.FormatInt(time.Now().UnixNano(), 10) + ".png"
	cmd := exec.Command("screencapture", "-x", "-t", "png", tempFile)
	err := cmd.Run()
	if err != nil {
		return nil, fmt.Errorf("screencapture命令执行失败: %v", err)
	}

	// 确保文件在函数结束时被删除
	defer os.Remove(tempFile)

	// 读取截图文件
	file, err := os.Open(tempFile)
	if err != nil {
		return nil, fmt.Errorf("无法打开截图文件: %v", err)
	}
	defer file.Close()

	// 解码PNG图片
	img, err := png.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("无法解码PNG图片: %v", err)
	}

	return img, nil
}

// macOS指定显示器截图实现
func captureDisplayMacOS(displayIndex int) (image.Image, error) {
	// 获取显示器信息
	monitors := getMonitorInfo()
	if displayIndex >= len(monitors) {
		return nil, fmt.Errorf("显示器索引超出范围: %d >= %d", displayIndex, len(monitors))
	}

	monitor := monitors[displayIndex]

	// 使用screencapture命令截取指定显示器
	tempFile := "/tmp/screenshot_display_" + strconv.FormatInt(time.Now().UnixNano(), 10) + ".png"

	// 构建screencapture命令，使用-D参数指定显示器
	cmd := exec.Command("screencapture", "-x", "-t", "png", "-D", strconv.Itoa(displayIndex+1), tempFile)
	err := cmd.Run()
	if err != nil {
		// 如果-D参数失败，回退到区域截图
		return captureRegionMacOS(monitor.X, monitor.Y, monitor.Width, monitor.Height)
	}

	// 确保文件在函数结束时被删除
	defer os.Remove(tempFile)

	// 读取截图文件
	file, err := os.Open(tempFile)
	if err != nil {
		return nil, fmt.Errorf("无法打开截图文件: %v", err)
	}
	defer file.Close()

	// 解码PNG图片
	img, err := png.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("无法解码PNG图片: %v", err)
	}

	return img, nil
}

// macOS活动窗口截图实现
func captureActiveWindowMacOS() (image.Image, error) {
	// 使用screencapture命令截取活动窗口
	tempFile := "/tmp/screenshot_window_" + strconv.FormatInt(time.Now().UnixNano(), 10) + ".png"
	cmd := exec.Command("screencapture", "-x", "-t", "png", "-w", tempFile)
	err := cmd.Run()
	if err != nil {
		return nil, fmt.Errorf("screencapture窗口截图命令执行失败: %v", err)
	}

	// 确保文件在函数结束时被删除
	defer os.Remove(tempFile)

	// 读取截图文件
	file, err := os.Open(tempFile)
	if err != nil {
		return nil, fmt.Errorf("无法打开截图文件: %v", err)
	}
	defer file.Close()

	// 解码PNG图片
	img, err := png.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("无法解码PNG图片: %v", err)
	}

	return img, nil
}

// macOS区域截图实现
func captureRegionMacOS(x, y, width, height int) (image.Image, error) {
	// 使用screencapture命令截取指定区域
	tempFile := "/tmp/screenshot_region_" + strconv.FormatInt(time.Now().UnixNano(), 10) + ".png"

	// 构建区域参数：-R x,y,width,height
	regionParam := fmt.Sprintf("%d,%d,%d,%d", x, y, width, height)
	cmd := exec.Command("screencapture", "-x", "-t", "png", "-R", regionParam, tempFile)
	err := cmd.Run()
	if err != nil {
		return nil, fmt.Errorf("screencapture区域截图命令执行失败: %v", err)
	}

	// 确保文件在函数结束时被删除
	defer os.Remove(tempFile)

	// 读取截图文件
	file, err := os.Open(tempFile)
	if err != nil {
		return nil, fmt.Errorf("无法打开截图文件: %v", err)
	}
	defer file.Close()

	// 解码PNG图片
	img, err := png.Decode(file)
	if err != nil {
		return nil, fmt.Errorf("无法解码PNG图片: %v", err)
	}

	return img, nil
}

// 图片编码函数（使用内存池优化）
func encodeImage(img image.Image, format string, quality int) ([]byte, error) {
	// 使用内存池获取压缩缓冲区
	compressBuf := make([]byte, 512*1024) // 临时分配，因为这是全局函数
	buf := bytes.NewBuffer(compressBuf[:0])

	switch strings.ToLower(format) {
	case "png", "":
		err := png.Encode(buf, img)
		if err != nil {
			return nil, fmt.Errorf("PNG编码失败: %v", err)
		}
	case "jpeg", "jpg":
		if quality <= 0 || quality > 100 {
			quality = 85 // 提高默认质量
		}
		err := jpeg.Encode(buf, img, &jpeg.Options{Quality: quality})
		if err != nil {
			return nil, fmt.Errorf("JPEG编码失败: %v", err)
		}
	default:
		return nil, fmt.Errorf("不支持的图片格式: %s", format)
	}

	// 创建返回副本
	result := make([]byte, buf.Len())
	copy(result, buf.Bytes())
	return result, nil
}

// encodeMacOSImage 使用高性能编码macOS截图结果
// 🚀 性能优化：优先使用硬件加速的编码
func encodeMacOSImage(result *C.MacOSScreenshotResult, format string, quality int) ([]byte, int, int, error) {
	width := int(result.width)
	height := int(result.height)

	switch strings.ToLower(format) {
	case "jpeg", "jpg":
		if quality <= 0 || quality > 100 {
			quality = 85
		}

		// 🚀 性能优化：使用macOS硬件加速的JPEG编码
		encodeResult := C.encode_jpeg_macos_fast((*C.uchar)(result.data),
			C.int(width), C.int(height), C.int(quality))
		defer C.free_macos_encode_result(encodeResult)

		if encodeResult.success != 0 {
			data := C.GoBytes(unsafe.Pointer(encodeResult.data), C.int(encodeResult.size))
			return data, width, height, nil
		}

		// 如果硬件编码失败，回退到软件编码
		fallthrough

	case "png", "":
		// 转换C结果为Go图像
		img := convertMacOSResultToImage(result)
		imageData, err := encodeImage(img, format, quality)
		if err != nil {
			return nil, 0, 0, fmt.Errorf("图片编码失败: %v", err)
		}
		return imageData, width, height, nil

	default:
		return nil, 0, 0, fmt.Errorf("不支持的图片格式: %s", format)
	}
}

// convertMacOSResultToImage 将macOS C结果转换为Go图像
func convertMacOSResultToImage(result *C.MacOSScreenshotResult) image.Image {
	width := int(result.width)
	height := int(result.height)

	// 创建RGBA图像
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 高效的内存拷贝
	dataSize := width * height * 4 // RGBA
	C.memcpy(unsafe.Pointer(&img.Pix[0]), unsafe.Pointer(result.data), C.size_t(dataSize))

	return img
}

// handleScreenshotRequest方法在handle_screenshot.go中定义（CGO版本）
