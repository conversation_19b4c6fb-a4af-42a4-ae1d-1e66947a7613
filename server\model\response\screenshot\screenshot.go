package screenshot

import (
	"server/model/basic"
)

// ScreenshotResponse 截图响应
type ScreenshotResponse struct {
	TaskID    uint64   `json:"task_id"`    // 任务ID
	Success   bool   `json:"success"`    // 是否成功
	Error     string `json:"error"`      // 错误信息
	ImageData []byte `json:"image_data"` // 图片数据
	Width     int    `json:"width"`      // 图片宽度
	Height    int    `json:"height"`     // 图片高度
	Format    string `json:"format"`     // 图片格式
	Size      int64  `json:"size"`       // 文件大小
	Timestamp int64  `json:"timestamp"`  // 截图时间戳
}

// ScreenshotListResponse 截图列表响应
type ScreenshotListResponse struct {
	TaskID      uint64                   `json:"task_id"`     // 任务ID
	Success     bool                   `json:"success"`     // 操作是否成功
	Screenshots []basic.ScreenshotInfo `json:"screenshots"` // 截图列表
	Error       string                 `json:"error"`       // 错误信息
	Count       int                    `json:"count"`       // 截图总数
}

// ScreenStreamStartResponse 屏幕流开始响应
type ScreenStreamStartResponse struct {
	TaskID   uint64   `json:"task_id"`   // 任务ID
	Success  bool   `json:"success"`   // 操作是否成功
	Error    string `json:"error"`     // 错误信息
	StreamID string `json:"stream_id"` // 流ID
}

// ScreenStreamStopResponse 屏幕流停止响应
type ScreenStreamStopResponse struct {
	TaskID  uint64   `json:"task_id"` // 任务ID
	Success bool   `json:"success"` // 操作是否成功
	Error   string `json:"error"`   // 错误信息
}

// ScreenStreamDataResponse 屏幕流数据响应
type ScreenStreamDataResponse struct {
	TaskID    uint64   `json:"task_id"`    // 任务ID
	Success   bool   `json:"success"`    // 操作是否成功
	StreamID  string `json:"stream_id"`  // 流ID
	FrameData []byte `json:"frame_data"` // 帧数据
	Width     int    `json:"width"`      // 图片宽度
	Height    int    `json:"height"`     // 图片高度
	Format    string `json:"format"`     // 图片格式
	Size      int64  `json:"size"`       // 帧大小
	Timestamp int64  `json:"timestamp"`  // 时间戳
	Error     string `json:"error"`      // 错误信息
}

// MonitorListResponse 显示器列表响应
type MonitorListResponse struct {
	TaskID   uint64                `json:"task_id"`  // 任务ID
	Success  bool                `json:"success"`  // 操作是否成功
	Monitors []basic.MonitorInfo `json:"monitors"` // 显示器列表
	Error    string              `json:"error"`    // 错误信息
	Count    int                 `json:"count"`    // 显示器数量
}
