//go:build windows
// +build windows

package common

import (
	"encoding/json"
	"fmt"
	"log"
	"os/exec"
	"strings"
	"syscall"
	"time"

	"github.com/shirou/gopsutil/v3/process"
	"golang.org/x/sys/windows"
)

// ProcessDetailsRequest 进程详情请求
type ProcessDetailsRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
	PID    int32  `json:"pid"`
}

// ProcessSuspendRequest 挂起进程请求
type ProcessSuspendRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
	PID    int32  `json:"pid"`
}

// ProcessResumeRequest 恢复进程请求
type ProcessResumeRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
	PID    int32  `json:"pid"`
}

// ProcessSuspendResponse 挂起进程响应
type ProcessSuspendResponse struct {
	TaskID  uint64 `json:"task_id"` // 任务ID
	Success bool   `json:"success"` // 操作是否成功
	PID     int32  `json:"pid"`     // 进程ID
	Error   string `json:"error"`   // 错误信息
}

// ProcessResumeResponse 恢复进程响应
type ProcessResumeResponse struct {
	TaskID  uint64 `json:"task_id"` // 任务ID
	Success bool   `json:"success"` // 操作是否成功
	PID     int32  `json:"pid"`     // 进程ID
	Error   string `json:"error"`   // 错误信息
}

// ProcessKillRequest 终止进程请求
type ProcessKillRequest struct {
	TaskID uint64 `json:"task_id"` // 任务ID
	PID    int32  `json:"pid"`
	Force  bool   `json:"force"`
}

// ProcessKillResponse 终止进程响应
type ProcessKillResponse struct {
	TaskID  uint64 `json:"task_id"` // 任务ID
	Success bool   `json:"success"` // 操作是否成功
	PID     int32  `json:"pid"`     // 进程ID
	Error   string `json:"error"`   // 错误信息
}

// ProcessStartResponse 启动进程响应
type ProcessStartResponse struct {
	TaskID     uint64 `json:"task_id"`    // 任务ID
	Success    bool   `json:"success"`    // 操作是否成功
	PID        int32  `json:"pid"`        // 新进程ID
	Error      string `json:"error"`      // 错误信息
	ExitCode   int    `json:"exit_code"`  // 退出码
	Output     string `json:"output"`     // 进程输出
	Executable string `json:"executable"` // 实际执行的文件路径
}

// ProcessDetailsResponse 进程详情响应
type ProcessDetailsResponse struct {
	TaskID      uint64           `json:"task_id"`     // 任务ID
	Success     bool             `json:"success"`     // 操作是否成功
	Process     *ProcessFullInfo `json:"process"`     // 进程基本信息
	Modules     []interface{}    `json:"modules"`     // 加载的模块
	Connections []interface{}    `json:"connections"` // 网络连接
	OpenFiles   []interface{}    `json:"open_files"`  // 打开的文件
	Error       string           `json:"error"`       // 错误信息
}

// ProcessStartRequest 启动进程请求
type ProcessStartRequest struct {
	TaskID     uint64 `json:"task_id"` // 任务ID
	Command    string `json:"command"`
	Args       string `json:"args"`
	WorkDir    string `json:"workDir"`
	RunAsAdmin bool   `json:"runAsAdmin"`
	HideWindow bool   `json:"hideWindow"`
}

// handleProcessDetails 处理获取进程详情请求
func (cm *ConnectionManager) handleProcessDetails(packet *Packet) {
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessDetailsResponse{
		TaskID:      0,
		Success:     false,
		Process:     nil,
		Modules:     []interface{}{},
		Connections: []interface{}{},
		OpenFiles:   []interface{}{},
		Error:       "解析请求失败",
	}

	var req ProcessDetailsRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析进程详情请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		cm.sendProcessResponse(ProcessDetails, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID
	errorResp.TaskID = req.TaskID

	ProcessFullInfo, err := cm.getProcessDetails(req.PID)
	if err != nil {
		log.Printf("获取进程详情失败: %v", err)
		// 更新错误响应的错误信息
		errorResp.Error = fmt.Sprintf("获取进程详情失败: %v", err)
		cm.sendProcessResponse(ProcessDetails, false, fmt.Sprintf("获取进程详情失败: %v", err), errorResp)
		return
	}

	detailsResp := ProcessDetailsResponse{
		TaskID:      req.TaskID,
		Success:     true,
		Process:     ProcessFullInfo,
		Modules:     []interface{}{},
		Connections: []interface{}{},
		OpenFiles:   []interface{}{},
		Error:       "",
	}
	cm.sendProcessResponse(ProcessDetails, true, "获取进程详情成功", detailsResp)
}

// getProcessDetails 获取进程详细信息
func (cm *ConnectionManager) getProcessDetails(pid int32) (*ProcessFullInfo, error) {
	// 🚀 性能优化：检查进程信息缓存
	processCacheMutex.RLock()
	if cachedInfo, exists := processInfoCache[pid]; exists {
		// 检查缓存是否过期
		if time.Since(cachedInfo.StartTime) < cacheExpiry {
			// 创建缓存信息的副本
			info := processInfoPool.Get().(*ProcessFullInfo)
			*info = *cachedInfo
			processCacheMutex.RUnlock()
			return info, nil
		}
	}
	processCacheMutex.RUnlock()

	p, err := process.NewProcess(pid)
	if err != nil {
		return nil, err
	}

	info, err := cm.getProcessFullInfo(p)
	if err != nil {
		return nil, err
	}

	// 🚀 性能优化：将结果缓存
	processCacheMutex.Lock()
	processInfoCache[pid] = info
	processCacheMutex.Unlock()

	return info, nil
}

// handleProcessKill 处理终止进程请求
func (cm *ConnectionManager) handleProcessKill(packet *Packet) {
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessKillResponse{
		TaskID:  0,
		Success: false,
		PID:     0,
		Error:   "解析请求失败",
	}

	var req ProcessKillRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析终止进程请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		cm.sendProcessResponse(ProcessKill, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID和PID
	errorResp.TaskID = req.TaskID
	errorResp.PID = req.PID

	err := cm.killProcess(req.PID, req.Force)
	if err != nil {
		log.Printf("终止进程失败: %v", err)
		// 更新错误响应的错误信息
		errorResp.Error = fmt.Sprintf("终止进程失败: %v", err)
		cm.sendProcessResponse(ProcessKill, false, fmt.Sprintf("终止进程失败: %v", err), errorResp)
		return
	}
	// 构造成功响应
	response := ProcessKillResponse{
		TaskID:  req.TaskID,
		Success: true,
		PID:     req.PID,
		Error:   "",
	}
	cm.sendProcessResponse(ProcessKill, true, fmt.Sprintf("进程 %d 已终止", req.PID), response)
}

// killProcess 终止进程
func (cm *ConnectionManager) killProcess(pid int32, force bool) error {
	p, err := process.NewProcess(pid)
	if err != nil {
		return err
	}

	if force {
		return p.Kill()
	} else {
		return p.Terminate()
	}
}

// handleProcessStart 处理启动进程请求
func (cm *ConnectionManager) handleProcessStart(packet *Packet) {
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessStartResponse{
		TaskID:     0,
		Success:    false,
		PID:        0,
		Error:      "解析请求失败",
		ExitCode:   0,
		Output:     "",
		Executable: "",
	}

	var req ProcessStartRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析启动进程请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		cm.sendProcessResponse(ProcessStart, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID和Executable
	errorResp.TaskID = req.TaskID
	errorResp.Executable = req.Command

	pid, err := cm.startProcess(req)
	if err != nil {
		log.Printf("启动进程失败: %v", err)
		// 更新错误响应的错误信息
		errorResp.Error = fmt.Sprintf("启动进程失败: %v", err)
		cm.sendProcessResponse(ProcessStart, false, fmt.Sprintf("启动进程失败: %v", err), errorResp)
		return
	}

	startResp := ProcessStartResponse{
		TaskID:     req.TaskID,
		Success:    true,
		PID:        int32(pid),
		Error:      "",
		ExitCode:   0,
		Output:     "",
		Executable: req.Command,
	}
	cm.sendProcessResponse(ProcessStart, true, fmt.Sprintf("进程启动成功，PID: %d", pid), startResp)
}

// startProcess 启动进程
func (cm *ConnectionManager) startProcess(req ProcessStartRequest) (int, error) {
	var cmd *exec.Cmd

	if req.Args != "" {
		args := strings.Fields(req.Args)
		cmd = exec.Command(req.Command, args...)
	} else {
		cmd = exec.Command(req.Command)
	}

	if req.WorkDir != "" {
		cmd.Dir = req.WorkDir
	}

	// Windows下的提权处理
	if req.RunAsAdmin {
		// 使用runas执行
		originalCommand := req.Command
		originalArgs := req.Args
		cmdLine := originalCommand
		if originalArgs != "" {
			cmdLine += " " + originalArgs
		}
		cmd = exec.Command("runas", "/user:Administrator", cmdLine)
	}

	// 设置Windows特定的属性
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow: req.HideWindow,
	}

	err := cmd.Start()
	if err != nil {
		return 0, err
	}

	return cmd.Process.Pid, nil
}

// handleProcessSuspend 处理挂起进程请求
func (cm *ConnectionManager) handleProcessSuspend(packet *Packet) {
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessSuspendResponse{
		TaskID:  0,
		Success: false,
		PID:     0,
		Error:   "解析请求失败",
	}

	var req ProcessSuspendRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析挂起进程请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		errorResp.TaskID = req.TaskID
		cm.sendProcessResponse(ProcessSuspend, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID和PID
	errorResp.TaskID = req.TaskID
	errorResp.PID = req.PID

	err := cm.suspendProcess(req.PID)
	if err != nil {
		log.Printf("挂起进程失败: %v", err)
		// 更新错误响应的错误信息
		errorResp.Error = fmt.Sprintf("挂起进程失败: %v", err)
		cm.sendProcessResponse(ProcessSuspend, false, fmt.Sprintf("挂起进程失败: %v", err), errorResp)
		return
	}

	response := ProcessSuspendResponse{
		TaskID:  req.TaskID,
		Success: true,
		PID:     req.PID,
		Error:   "",
	}
	cm.sendProcessResponse(ProcessSuspend, true, fmt.Sprintf("进程 %d 已挂起", req.PID), response)
}

// suspendProcess 挂起进程 (Windows)
func (cm *ConnectionManager) suspendProcess(pid int32) error {
	handle, err := windows.OpenProcess(windows.PROCESS_SUSPEND_RESUME, false, uint32(pid))
	if err != nil {
		return err
	}
	defer windows.CloseHandle(handle)

	// 使用NtSuspendProcess API
	ntdll := windows.NewLazySystemDLL("ntdll.dll")
	ntSuspendProcess := ntdll.NewProc("NtSuspendProcess")

	ret, _, _ := ntSuspendProcess.Call(uintptr(handle))
	if ret != 0 {
		return fmt.Errorf("NtSuspendProcess failed with status: 0x%x", ret)
	}

	// 🚀 性能优化：挂起成功后，将进程添加到挂起缓存中
	suspendedMutex.Lock()
	suspendedProcesses[pid] = time.Now()
	suspendedMutex.Unlock()

	return nil
}

// handleProcessResume 处理恢复进程请求
func (cm *ConnectionManager) handleProcessResume(packet *Packet) {
	// 创建错误响应结构体，TaskID初始化为0
	errorResp := ProcessResumeResponse{
		TaskID:  0,
		Success: false,
		PID:     0,
		Error:   "解析请求失败",
	}

	var req ProcessResumeRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("解析恢复进程请求失败: %v", err)
		// 反序列化失败时，TaskID保持为0
		cm.sendProcessResponse(ProcessResume, false, "解析请求失败", errorResp)
		return
	}

	// 反序列化成功后，更新错误响应的TaskID和PID
	errorResp.TaskID = req.TaskID
	errorResp.PID = req.PID

	err := cm.resumeProcess(req.PID)
	if err != nil {
		log.Printf("恢复进程失败: %v", err)
		// 更新错误响应的错误信息
		errorResp.Error = fmt.Sprintf("恢复进程失败: %v", err)
		cm.sendProcessResponse(ProcessResume, false, fmt.Sprintf("恢复进程失败: %v", err), errorResp)
		return
	}

	response := ProcessResumeResponse{
		TaskID:  req.TaskID,
		Success: true,
		PID:     req.PID,
		Error:   "",
	}
	cm.sendProcessResponse(ProcessResume, true, fmt.Sprintf("进程 %d 已恢复", req.PID), response)
}

// resumeProcess 恢复进程 (Windows)
func (cm *ConnectionManager) resumeProcess(pid int32) error {
	handle, err := windows.OpenProcess(windows.PROCESS_SUSPEND_RESUME, false, uint32(pid))
	if err != nil {
		return err
	}
	defer windows.CloseHandle(handle)

	// 使用NtResumeProcess API
	ntdll := windows.NewLazySystemDLL("ntdll.dll")
	ntResumeProcess := ntdll.NewProc("NtResumeProcess")

	ret, _, _ := ntResumeProcess.Call(uintptr(handle))
	if ret != 0 {
		return fmt.Errorf("NtResumeProcess failed with status: 0x%x", ret)
	}

	// 🚀 性能优化：恢复成功后，从挂起缓存中移除进程
	suspendedMutex.Lock()
	delete(suspendedProcesses, pid)
	suspendedMutex.Unlock()

	return nil
}
