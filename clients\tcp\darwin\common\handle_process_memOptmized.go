//go:build darwin
// +build darwin
package common

import (
	"log"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"github.com/shirou/gopsutil/v3/mem"
)

// 内存优化相关的全局变量
var (
	// 内存统计计数器
	memoryStats struct {
		allocatedObjects int64 // 已分配对象数
		releasedObjects  int64 // 已释放对象数
		cacheHits        int64 // 缓存命中数
		cacheMisses      int64 // 缓存未命中数
		gcTriggerCount   int64 // GC触发次数
		lastMemoryCheck  time.Time
		memoryCheckMutex sync.RWMutex
	}

	// 内存监控配置
	memoryThresholds = struct {
		warningPercent   float64 // 内存使用警告阈值
		criticalPercent  float64 // 内存使用危险阈值
		gcTriggerPercent float64 // 触发GC的内存阈值
	}{
		warningPercent:   75.0,
		criticalPercent:  85.0,
		gcTriggerPercent: 80.0,
	}

	// 自适应缓存大小控制
	adaptiveCacheConfig = struct {
		maxCacheSize     int
		minCacheSize     int
		currentCacheSize int
		mutex            sync.RWMutex
	}{
		maxCacheSize:     2000,
		minCacheSize:     100,
		currentCacheSize: 500,
	}
)

// init 初始化内存优化模块
func init() {
	log.Printf("🚀 Linux内存优化模块初始化")

	// 启动内存监控goroutine
	go startMemoryMonitor()

	// 启动自适应缓存调整
	go startAdaptiveCacheManager()

	log.Printf("✅ 内存优化模块初始化完成")
}

// startMemoryMonitor 启动内存监控
func startMemoryMonitor() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次内存
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			checkMemoryUsage()
		}
	}
}

// checkMemoryUsage 检查内存使用情况
func checkMemoryUsage() {
	memoryStats.memoryCheckMutex.Lock()
	defer memoryStats.memoryCheckMutex.Unlock()

	// 获取系统内存信息
	vmem, err := mem.VirtualMemory()
	if err != nil {
		log.Printf("⚠️  获取内存信息失败: %v", err)
		return
	}

	usedPercent := vmem.UsedPercent
	memoryStats.lastMemoryCheck = time.Now()

	// 记录内存使用统计
	allocated := atomic.LoadInt64(&memoryStats.allocatedObjects)
	released := atomic.LoadInt64(&memoryStats.releasedObjects)
	cacheHits := atomic.LoadInt64(&memoryStats.cacheHits)
	cacheMisses := atomic.LoadInt64(&memoryStats.cacheMisses)

	log.Printf("📊 内存监控: 使用率=%.1f%%, 对象池=[分配:%d,释放:%d], 缓存=[命中:%d,未命中:%d]",
		usedPercent, allocated, released, cacheHits, cacheMisses)

	// 内存使用率检查和优化
	if usedPercent > memoryThresholds.criticalPercent {
		log.Printf("🚨 内存使用率危险 (%.1f%% > %.1f%%)，执行紧急清理", usedPercent, memoryThresholds.criticalPercent)
		triggerEmergencyCleanup()
	} else if usedPercent > memoryThresholds.warningPercent {
		log.Printf("⚠️  内存使用率警告 (%.1f%% > %.1f%%)，执行预防性清理", usedPercent, memoryThresholds.warningPercent)
		triggerPreventiveCleanup()
	} else if usedPercent > memoryThresholds.gcTriggerPercent {
		log.Printf("🧹 内存使用率较高 (%.1f%% > %.1f%%)，触发GC", usedPercent, memoryThresholds.gcTriggerPercent)
		triggerGC()
	}
}

// triggerEmergencyCleanup 触发紧急清理
func triggerEmergencyCleanup() {
	atomic.AddInt64(&memoryStats.gcTriggerCount, 1)

	// 强制清理所有缓存
	cleanupCache()

	// 调整缓存大小到最小值
	adaptiveCacheConfig.mutex.Lock()
	adaptiveCacheConfig.currentCacheSize = adaptiveCacheConfig.minCacheSize
	adaptiveCacheConfig.mutex.Unlock()

	// 强制GC
	runtime.GC()
	runtime.GC() // 执行两次确保彻底清理

	log.Printf("🚨 紧急清理完成，缓存大小调整为: %d", adaptiveCacheConfig.minCacheSize)
}

// triggerPreventiveCleanup 触发预防性清理
func triggerPreventiveCleanup() {
	// 清理过期缓存
	cleanupCache()

	// 适度减少缓存大小
	adaptiveCacheConfig.mutex.Lock()
	if adaptiveCacheConfig.currentCacheSize > adaptiveCacheConfig.minCacheSize {
		adaptiveCacheConfig.currentCacheSize = int(float64(adaptiveCacheConfig.currentCacheSize) * 0.8)
		if adaptiveCacheConfig.currentCacheSize < adaptiveCacheConfig.minCacheSize {
			adaptiveCacheConfig.currentCacheSize = adaptiveCacheConfig.minCacheSize
		}
	}
	adaptiveCacheConfig.mutex.Unlock()

	// 触发GC
	triggerGC()

	log.Printf("⚠️  预防性清理完成，缓存大小调整为: %d", adaptiveCacheConfig.currentCacheSize)
}

// triggerGC 触发垃圾回收
func triggerGC() {
	atomic.AddInt64(&memoryStats.gcTriggerCount, 1)
	runtime.GC()
	log.Printf("🧹 GC执行完成，累计触发次数: %d", atomic.LoadInt64(&memoryStats.gcTriggerCount))
}

// startAdaptiveCacheManager 启动自适应缓存管理
func startAdaptiveCacheManager() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒调整一次缓存大小
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			adjustCacheSize()
		}
	}
}

// adjustCacheSize 自适应调整缓存大小
func adjustCacheSize() {
	cacheHits := atomic.LoadInt64(&memoryStats.cacheHits)
	cacheMisses := atomic.LoadInt64(&memoryStats.cacheMisses)

	if cacheHits+cacheMisses == 0 {
		return // 没有缓存活动
	}

	hitRate := float64(cacheHits) / float64(cacheHits+cacheMisses) * 100

	adaptiveCacheConfig.mutex.Lock()
	defer adaptiveCacheConfig.mutex.Unlock()

	oldSize := adaptiveCacheConfig.currentCacheSize

	// 根据命中率调整缓存大小
	if hitRate > 90 {
		// 命中率很高，可以适当增加缓存
		adaptiveCacheConfig.currentCacheSize = int(float64(adaptiveCacheConfig.currentCacheSize) * 1.1)
		if adaptiveCacheConfig.currentCacheSize > adaptiveCacheConfig.maxCacheSize {
			adaptiveCacheConfig.currentCacheSize = adaptiveCacheConfig.maxCacheSize
		}
	} else if hitRate < 60 {
		// 命中率较低，减少缓存大小
		adaptiveCacheConfig.currentCacheSize = int(float64(adaptiveCacheConfig.currentCacheSize) * 0.9)
		if adaptiveCacheConfig.currentCacheSize < adaptiveCacheConfig.minCacheSize {
			adaptiveCacheConfig.currentCacheSize = adaptiveCacheConfig.minCacheSize
		}
	}

	if oldSize != adaptiveCacheConfig.currentCacheSize {
		log.Printf("🔧 自适应缓存调整: %d -> %d (命中率: %.1f%%)",
			oldSize, adaptiveCacheConfig.currentCacheSize, hitRate)
	}
}

// recordCacheHit 记录缓存命中
func recordCacheHit() {
	atomic.AddInt64(&memoryStats.cacheHits, 1)
}

// recordCacheMiss 记录缓存未命中
func recordCacheMiss() {
	atomic.AddInt64(&memoryStats.cacheMisses, 1)
}

// recordObjectAllocation 记录对象分配
func recordObjectAllocation() {
	atomic.AddInt64(&memoryStats.allocatedObjects, 1)
}

// recordObjectRelease 记录对象释放
func recordObjectRelease() {
	atomic.AddInt64(&memoryStats.releasedObjects, 1)
}

// getCurrentCacheSize 获取当前缓存大小限制
func getCurrentCacheSize() int {
	adaptiveCacheConfig.mutex.RLock()
	defer adaptiveCacheConfig.mutex.RUnlock()
	return adaptiveCacheConfig.currentCacheSize
}

// getMemoryStats 获取内存统计信息
func getMemoryStats() map[string]interface{} {
	return map[string]interface{}{
		"allocated_objects":  atomic.LoadInt64(&memoryStats.allocatedObjects),
		"released_objects":   atomic.LoadInt64(&memoryStats.releasedObjects),
		"cache_hits":         atomic.LoadInt64(&memoryStats.cacheHits),
		"cache_misses":       atomic.LoadInt64(&memoryStats.cacheMisses),
		"gc_trigger_count":   atomic.LoadInt64(&memoryStats.gcTriggerCount),
		"current_cache_size": getCurrentCacheSize(),
		"last_memory_check":  memoryStats.lastMemoryCheck,
	}
}
