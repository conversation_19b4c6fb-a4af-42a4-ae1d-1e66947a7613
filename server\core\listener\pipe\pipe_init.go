package pipe

import (
	"fmt"
	"go.uber.org/zap"
	"server/core/dbpool"
	"server/global"
	"server/model/sys"
	"gorm.io/gorm"
)

// InitPipeListeners 初始化所有状态为启用的pipe类型监听器
func InitPipeListeners() {
	// 🚀 从数据库中获取所有状态为启用的pipe类型监听器
	var listeners []sys.Listener
	if err := dbpool.ExecuteDBOperationAsyncAndWait("pipe_listeners_init", func(db *gorm.DB) error {
		return db.Where("type = ? AND status = ?", "pipe", 1).Find(&listeners).Error
	}); err != nil {
		global.LOG.Error("获取pipe监听器列表失败", zap.Error(err))
		return
	}

	// 启动所有监听器
	for _, l := range listeners {
		PipeManager.StartListener(l)
	}

	global.LOG.Info(fmt.Sprintf("已启动 %d 个pipe监听器", len(listeners)))
}
