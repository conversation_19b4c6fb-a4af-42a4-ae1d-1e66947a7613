//go:build darwin
// +build darwin

package common

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"time"

	psutilNet "github.com/shirou/gopsutil/v3/net"
	"github.com/shirou/gopsutil/v3/process"
)

// 网络监控并发控制
var (
	networkMutex        sync.Mutex // 网络监控请求互斥锁
	isNetworkProcessing bool       // 是否正在处理网络监控请求
)

// 网络连接数量限制
const (
	MaxNetworkConnections = 1000 // 最大返回连接数
)

// 网络监控相关结构体（与Linux版本相同）
type NetworkStatsRequest struct {
	TaskID uint64 `json:"task_id"`
}

type NetworkInterfacesRequest struct {
	TaskID uint64 `json:"task_id"`
}

type NetworkConnectionsRequest struct {
	TaskID   uint64 `json:"task_id"`
	Protocol string `json:"protocol"`
	State    string `json:"state"`
}

type CloseConnectionRequest struct {
	TaskID       uint64 `json:"task_id"`
	ConnectionID string `json:"connection_id"`
	LocalAddr    string `json:"local_addr"`
	LocalPort    int    `json:"local_port"`
	RemoteAddr   string `json:"remote_addr"`
	RemotePort   int    `json:"remote_port"`
	Protocol     string `json:"protocol"`
}

type NetworkStats struct {
	UploadSpeed       float64 `json:"upload_speed"`
	DownloadSpeed     float64 `json:"download_speed"`
	ActiveConnections int     `json:"active_connections"`
	PacketLoss        float64 `json:"packet_loss"`
	TotalBytesSent    uint64  `json:"total_bytes_sent"`
	TotalBytesRecv    uint64  `json:"total_bytes_recv"`
	Timestamp         int64   `json:"timestamp"`
}

type NetInterface struct {
	Name          string `json:"name"`
	Type          string `json:"type"`
	Status        string `json:"status"`
	IPAddress     string `json:"ip_address"`
	MACAddress    string `json:"mac_address"`
	Speed         uint64 `json:"speed"`
	BytesSent     uint64 `json:"bytes_sent"`
	BytesReceived uint64 `json:"bytes_received"`
	PacketsSent   uint64 `json:"packets_sent"`
	PacketsRecv   uint64 `json:"packets_recv"`
	ErrorsIn      uint64 `json:"errors_in"`
	ErrorsOut     uint64 `json:"errors_out"`
	DropsIn       uint64 `json:"drops_in"`
	DropsOut      uint64 `json:"drops_out"`
}

type NetworkConnection struct {
	ID              string    `json:"id"`
	Protocol        string    `json:"protocol"`
	LocalAddress    string    `json:"local_address"`
	LocalPort       int       `json:"local_port"`
	RemoteAddress   string    `json:"remote_address"`
	RemotePort      int       `json:"remote_port"`
	State           string    `json:"state"`
	ProcessName     string    `json:"process_name"`
	PID             int       `json:"pid"`
	EstablishedTime time.Time `json:"established_time"`
	Duration        string    `json:"duration"`
	BytesSent       uint64    `json:"bytes_sent"`
	BytesReceived   uint64    `json:"bytes_received"`
}

// 响应结构体
type NetworkStatsResponse struct {
	TaskID  uint64       `json:"task_id"`
	Success bool         `json:"success"`
	Error   string       `json:"error"`
	Stats   NetworkStats `json:"stats"`
}

type NetworkInterfacesResponse struct {
	TaskID     uint64         `json:"task_id"`
	Success    bool           `json:"success"`
	Error      string         `json:"error"`
	Interfaces []NetInterface `json:"interfaces"`
}

type NetworkConnectionsResponse struct {
	TaskID      uint64              `json:"task_id"`
	Success     bool                `json:"success"`
	Error       string              `json:"error"`
	Connections []NetworkConnection `json:"connections"`
	Total       int                 `json:"total"`
}

type CloseConnectionResponse struct {
	TaskID  uint64 `json:"task_id"`
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Message string `json:"message"`
}

// handleNetworkRequest 处理网络监控请求的主入口
func (cm *ConnectionManager) handleNetworkRequest(packet *Packet) {
	log.Printf("🌐 handleNetworkRequest: 收到网络监控请求 Code=%d", packet.Header.Code)

	// 检查是否有其他网络监控请求正在处理
	log.Printf("🔒 handleNetworkRequest: 检查并发控制锁...")
	networkMutex.Lock()
	if isNetworkProcessing {
		networkMutex.Unlock()
		log.Printf("⚠️ handleNetworkRequest: 网络监控请求正在处理中，跳过当前请求: Code=%d", packet.Header.Code)
		// 发送忙碌响应
		cm.sendBusyResponse(packet)
		return
	}
	isNetworkProcessing = true
	networkMutex.Unlock()
	log.Printf("✅ handleNetworkRequest: 获取处理锁成功，开始处理请求")

	// 服务端现在会在处理网络监控响应时自动更新活动时间，无需客户端额外处理
	log.Printf("💓 handleNetworkRequest: 开始处理网络监控请求...")

	// 处理完成后释放锁
	defer func() {
		log.Printf("🔓 handleNetworkRequest: 释放处理锁")
		networkMutex.Lock()
		isNetworkProcessing = false
		networkMutex.Unlock()
		log.Printf("✅ handleNetworkRequest: 处理锁已释放")

		// 服务端现在会在处理响应时自动更新活动时间
		log.Printf("✅ handleNetworkRequest: 网络监控请求处理完成")
	}()

	log.Printf("🔍 handleNetworkRequest: 根据Code分发请求...")
	switch packet.Header.Code {
	case NetStatsCmd:
		log.Printf("📊 handleNetworkRequest: 处理网络统计信息请求")
		cm.handleNetworkStats(packet)
	case NetInterfacesCmd:
		log.Printf("🌐 handleNetworkRequest: 处理网络接口信息请求")
		cm.handleNetworkInterfaces(packet)
	case NetConnectionsCmd:
		log.Printf("🔗 handleNetworkRequest: 处理网络连接信息请求")
		cm.handleNetworkConnections(packet)
	case NetCloseConnCmd:
		log.Printf("❌ handleNetworkRequest: 处理关闭网络连接请求")
		cm.handleCloseConnection(packet)
	default:
		log.Printf("❓ handleNetworkRequest: 未知的网络监控操作代码: %d", packet.Header.Code)
	}
	log.Printf("🏁 handleNetworkRequest: 请求处理完成")
}

// handleNetworkStats 处理网络统计信息请求
func (cm *ConnectionManager) handleNetworkStats(packet *Packet) {
	log.Printf("🌐 开始处理网络统计信息请求")
	log.Printf("📦 数据包信息: Type=%d, Code=%d, Length=%d, DataSize=%d",
		packet.Header.Type, packet.Header.Code, packet.Header.Length, len(packet.PacketData.Data))

	errorResp := NetworkStatsResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	log.Printf("🔍 开始解析请求数据...")
	var req NetworkStatsRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析网络统计信息请求失败: %v", err)
		log.Printf("📄 原始数据: %s", string(packet.PacketData.Data))
		log.Printf("📤 发送错误响应: 请求格式错误")
		cm.sendResp(Network, NetStatsCmd, errorResp)
		return
	}

	log.Printf("📋 解析请求成功: TaskID=%d", req.TaskID)
	errorResp.TaskID = req.TaskID

	log.Printf("🔍 开始获取网络统计信息...")
	stats, err := cm.getNetworkStats()
	if err != nil {
		log.Printf("❌ 获取网络统计信息失败: %v", err)
		errorResp.Error = err.Error()
		log.Printf("📤 发送错误响应: TaskID=%d, Error=%s", errorResp.TaskID, errorResp.Error)
		cm.sendResp(Network, NetStatsCmd, errorResp)
		return
	}

	log.Printf("✅ 网络统计信息获取成功")
	if stats != nil {
		log.Printf("📊 统计信息: TotalBytesSent=%d, TotalBytesRecv=%d, UploadSpeed=%.2f, DownloadSpeed=%.2f, ActiveConnections=%d",
			stats.TotalBytesSent, stats.TotalBytesRecv, stats.UploadSpeed, stats.DownloadSpeed, stats.ActiveConnections)
	} else {
		log.Printf("⚠️ 统计信息为空")
	}

	response := NetworkStatsResponse{
		TaskID:  req.TaskID,
		Success: true,
		Stats:   *stats,
	}

	log.Printf("📤 准备发送成功响应: TaskID=%d", response.TaskID)

	// 添加调试日志：打印即将发送的响应数据
	if responseJSON, err := json.Marshal(response); err == nil {
		log.Printf("🔍 发送的响应JSON: %s", string(responseJSON))
	} else {
		log.Printf("⚠️ 无法序列化响应数据: %v", err)
	}

	// 检查连接状态
	if cm.conn == nil {
		log.Printf("❌ 连接为空，无法发送响应")
		return
	}

	log.Printf("🔗 连接状态检查: 连接存在，准备发送响应")
	cm.sendResp(Network, NetStatsCmd, response)

	// 发送后再次检查连接状态
	if cm.conn != nil {
		log.Printf("✅ 响应发送后连接仍然存在")
	} else {
		log.Printf("⚠️ 响应发送后连接已断开")
	}
	log.Printf("✅ 网络统计信息请求处理完成")
}

// handleNetworkInterfaces 处理网络接口信息请求
func (cm *ConnectionManager) handleNetworkInterfaces(packet *Packet) {
	log.Printf("🌐 开始处理网络接口信息请求")

	errorResp := NetworkInterfacesResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req NetworkInterfacesRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析网络接口信息请求失败: %v", err)
		cm.sendResp(Network, NetInterfacesCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	interfaces, err := cm.getNetworkInterfaces()
	if err != nil {
		log.Printf("❌ 获取网络接口信息失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendResp(Network, NetInterfacesCmd, errorResp)
		return
	}

	response := NetworkInterfacesResponse{
		TaskID:     req.TaskID,
		Success:    true,
		Interfaces: interfaces,
	}

	cm.sendResp(Network, NetInterfacesCmd, response)
	log.Printf("✅ 网络接口信息请求处理完成")
}

// handleNetworkConnections 处理网络连接信息请求
func (cm *ConnectionManager) handleNetworkConnections(packet *Packet) {
	log.Printf("🌐 开始处理网络连接信息请求")

	errorResp := NetworkConnectionsResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req NetworkConnectionsRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析网络连接信息请求失败: %v", err)
		cm.sendResp(Network, NetConnectionsCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	connections, err := cm.getNetworkConnections(req.Protocol, req.State)
	if err != nil {
		log.Printf("❌ 获取网络连接信息失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendResp(Network, NetConnectionsCmd, errorResp)
		return
	}

	response := NetworkConnectionsResponse{
		TaskID:      req.TaskID,
		Success:     true,
		Connections: connections,
		Total:       len(connections),
	}

	cm.sendResp(Network, NetConnectionsCmd, response)
	log.Printf("✅ 网络连接信息请求处理完成")
}

// handleCloseConnection 处理关闭网络连接请求
func (cm *ConnectionManager) handleCloseConnection(packet *Packet) {
	log.Printf("🌐 开始处理关闭网络连接请求")

	errorResp := CloseConnectionResponse{
		TaskID:  0,
		Success: false,
		Error:   "请求格式错误",
	}

	var req CloseConnectionRequest
	if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
		log.Printf("❌ 解析关闭网络连接请求失败: %v", err)
		cm.sendResp(Network, NetCloseConnCmd, errorResp)
		return
	}

	errorResp.TaskID = req.TaskID

	err := cm.closeNetworkConnection(req.ConnectionID, req.Protocol, req.LocalAddr, req.LocalPort, req.RemoteAddr, req.RemotePort)
	if err != nil {
		log.Printf("❌ 关闭网络连接失败: %v", err)
		errorResp.Error = err.Error()
		cm.sendResp(Network, NetCloseConnCmd, errorResp)
		return
	}

	response := CloseConnectionResponse{
		TaskID:  req.TaskID,
		Success: true,
		Message: "网络连接关闭成功",
	}

	cm.sendResp(Network, NetCloseConnCmd, response)
	log.Printf("✅ 关闭网络连接请求处理完成")
}

// sendBusyResponse 发送忙碌响应
func (cm *ConnectionManager) sendBusyResponse(packet *Packet) {
	var response interface{}

	switch packet.Header.Code {
	case NetStatsCmd:
		var req NetworkStatsRequest
		if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
			log.Printf("❌ 解析网络统计信息请求失败: %v", err)
			return
		}
		response = NetworkStatsResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	case NetInterfacesCmd:
		var req NetworkInterfacesRequest
		if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
			log.Printf("❌ 解析网络接口信息请求失败: %v", err)
			return
		}
		response = NetworkInterfacesResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	case NetConnectionsCmd:
		var req NetworkConnectionsRequest
		if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
			log.Printf("❌ 解析网络连接信息请求失败: %v", err)
			return
		}
		response = NetworkConnectionsResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	case NetCloseConnCmd:
		var req CloseConnectionRequest
		if err := json.Unmarshal(packet.PacketData.Data, &req); err != nil {
			log.Printf("❌ 解析关闭网络连接请求失败: %v", err)
			return
		}
		response = CloseConnectionResponse{
			TaskID:  req.TaskID,
			Success: false,
			Error:   "网络监控服务忙碌，请稍后重试",
		}
	default:
		return
	}

	cm.sendResp(Network, packet.Header.Code, response)
}

// getNetworkStats 获取网络统计信息
func (cm *ConnectionManager) getNetworkStats() (*NetworkStats, error) {
	log.Printf("🔍 getNetworkStats: 开始获取网络统计信息")

	stats := &NetworkStats{
		Timestamp: time.Now().Unix(),
	}
	log.Printf("📅 getNetworkStats: 创建统计对象，时间戳=%d", stats.Timestamp)

	// 尝试使用快速方法获取网络统计信息
	log.Printf("🔧 getNetworkStats: 尝试使用快速方法获取网络统计...")
	if fastStats, err := cm.getNetworkStatsFast(); err == nil {
		log.Printf("✅ getNetworkStats: 快速方法成功")
		return fastStats, nil
	} else {
		log.Printf("⚠️ getNetworkStats: 快速方法失败，回退到netstat: %v", err)
	}

	// 回退到netstat方法（添加超时控制）
	log.Printf("🔧 getNetworkStats: 执行 netstat -i 命令...")

	// 添加超时控制，防止命令执行时间过长
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "netstat", "-i")
	output, err := cmd.Output()
	if err != nil {
		if ctx.Err() == context.DeadlineExceeded {
			log.Printf("❌ getNetworkStats: netstat命令执行超时（15秒）")
			return nil, fmt.Errorf("netstat命令执行超时")
		}
		log.Printf("❌ getNetworkStats: 执行netstat命令失败: %v", err)
		return nil, fmt.Errorf("执行netstat命令失败: %v", err)
	}

	log.Printf("📄 getNetworkStats: netstat命令执行成功，输出长度=%d", len(output))
	log.Printf("📄 getNetworkStats: netstat输出前500字符: %s", string(output)[:min(500, len(output))])

	// 解析netstat输出
	lines := strings.Split(string(output), "\n")
	var totalBytesSent, totalBytesRecv uint64
	log.Printf("📊 getNetworkStats: 开始解析netstat输出，共%d行", len(lines))

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(line, "Link") || strings.Contains(line, "Name") {
			log.Printf("🔍 getNetworkStats: 跳过标题行[%d]: %s", i, line)
			continue
		}

		parts := strings.Fields(line)
		if len(parts) >= 10 {
			log.Printf("🔍 getNetworkStats: 解析行[%d]: %s", i, line)
			log.Printf("🔍 getNetworkStats: 字段数=%d, Ibytes=%s, Obytes=%s", len(parts), parts[6], parts[9])

			// macOS netstat -i 输出格式：Name Mtu Network Address Ipkts Ierrs Ibytes Opkts Oerrs Obytes Coll
			if recv, err := strconv.ParseUint(parts[6], 10, 64); err == nil {
				totalBytesRecv += recv
				log.Printf("📈 getNetworkStats: 接收字节累加: +%d, 总计=%d", recv, totalBytesRecv)
			} else {
				log.Printf("⚠️ getNetworkStats: 解析接收字节失败: %v", err)
			}
			if sent, err := strconv.ParseUint(parts[9], 10, 64); err == nil {
				totalBytesSent += sent
				log.Printf("📈 getNetworkStats: 发送字节累加: +%d, 总计=%d", sent, totalBytesSent)
			} else {
				log.Printf("⚠️ getNetworkStats: 解析发送字节失败: %v", err)
			}
		} else if len(parts) > 0 {
			log.Printf("🔍 getNetworkStats: 跳过字段不足的行[%d]: %s (字段数=%d)", i, line, len(parts))
		}
	}

	stats.TotalBytesSent = totalBytesSent
	stats.TotalBytesRecv = totalBytesRecv
	log.Printf("📊 getNetworkStats: 字节统计完成 - 发送=%d, 接收=%d", totalBytesSent, totalBytesRecv)

	// 获取活跃连接数（简化实现，避免递归调用）
	log.Printf("🔧 getNetworkStats: 执行 netstat -an 命令获取活跃连接...")

	// 为活跃连接统计也添加超时控制
	ctx2, cancel2 := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel2()

	cmd2 := exec.CommandContext(ctx2, "netstat", "-an")
	if output2, err2 := cmd2.Output(); err2 == nil {
		lines2 := strings.Split(string(output2), "\n")
		activeCount := 0
		log.Printf("📊 getNetworkStats: 开始统计活跃连接，共%d行", len(lines2))
		for _, line := range lines2 {
			if strings.Contains(line, "ESTABLISHED") {
				activeCount++
			}
		}
		stats.ActiveConnections = activeCount
		log.Printf("📊 getNetworkStats: 活跃连接统计完成，数量=%d", activeCount)
	} else {
		if ctx2.Err() == context.DeadlineExceeded {
			log.Printf("⚠️ getNetworkStats: 活跃连接统计超时（10秒）")
		} else {
			log.Printf("⚠️ getNetworkStats: 获取活跃连接失败: %v", err2)
		}
		stats.ActiveConnections = 0 // 设置默认值
	}

	// 简单的速度计算
	stats.UploadSpeed = float64(totalBytesSent) / 1024
	stats.DownloadSpeed = float64(totalBytesRecv) / 1024
	log.Printf("📊 getNetworkStats: 速度计算完成 - 上传=%.2fKB, 下载=%.2fKB", stats.UploadSpeed, stats.DownloadSpeed)

	log.Printf("✅ getNetworkStats: 网络统计信息获取完成")
	return stats, nil
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// updateLastActiveTime 更新客户端最后活动时间（用于防止心跳超时）
func (cm *ConnectionManager) updateLastActiveTime() {
	// 这个方法主要是为了在日志中显示我们正在处理活动时间
	// 实际的活动时间更新由心跳机制自动处理
	// 这里我们可以记录一个时间戳用于调试
	cm.mu.Lock()
	defer cm.mu.Unlock()
	// 可以在这里添加一些内部状态更新，但主要目的是防止并发心跳问题
}

// getNetworkStatsFast 使用系统API快速获取网络统计信息
func (cm *ConnectionManager) getNetworkStatsFast() (*NetworkStats, error) {
	log.Printf("🚀 getNetworkStatsFast: 开始快速获取网络统计")

	stats := &NetworkStats{
		Timestamp: time.Now().Unix(),
	}

	// 使用Go的net包获取网络接口信息
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("获取网络接口失败: %v", err)
	}

	log.Printf("🔍 getNetworkStatsFast: 找到%d个网络接口", len(interfaces))

	// 🚀 获取真实的网络统计数据，不使用任何虚构值
	totalBytesSent, totalBytesRecv, uploadSpeed, downloadSpeed, err := cm.getRealNetworkStats()
	if err != nil {
		log.Printf("⚠️ getNetworkStatsFast: 获取真实网络统计失败: %v", err)
		return nil, fmt.Errorf("获取真实网络统计失败: %v", err)
	}

	stats.TotalBytesSent = totalBytesSent
	stats.TotalBytesRecv = totalBytesRecv
	stats.UploadSpeed = uploadSpeed
	stats.DownloadSpeed = downloadSpeed

	// 快速获取活跃连接数
	log.Printf("🔧 getNetworkStatsFast: 快速获取活跃连接数...")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "sh", "-c", "netstat -an | grep ESTABLISHED | wc -l")
	if output, err := cmd.Output(); err == nil {
		if count, err := strconv.Atoi(strings.TrimSpace(string(output))); err == nil {
			stats.ActiveConnections = count
			log.Printf("📊 getNetworkStatsFast: 活跃连接数=%d", count)
		}
	} else {
		log.Printf("⚠️ getNetworkStatsFast: 获取活跃连接数失败: %v", err)
		stats.ActiveConnections = 0
	}

	// 注意：不再因为0值而回退，0值也是真实的统计数据
	log.Printf("📊 getNetworkStatsFast: 统计数据 - 发送:%d字节, 接收:%d字节, 上传速度:%.2fKB/s, 下载速度:%.2fKB/s",
		stats.TotalBytesSent, stats.TotalBytesRecv, stats.UploadSpeed, stats.DownloadSpeed)

	log.Printf("✅ getNetworkStatsFast: 快速获取完成")
	return stats, nil
}

// getNetworkInterfaces 获取网络接口信息（流式版本）
func (cm *ConnectionManager) getNetworkInterfaces() ([]NetInterface, error) {
	log.Printf("🔍 getNetworkInterfaces: 开始获取网络接口信息")

	// 清空缓存，确保获取最新数据
	cm.cachedNetstatOutput = ""

	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("获取网络接口失败: %v", err)
	}

	log.Printf("📊 getNetworkInterfaces: 找到%d个网络接口", len(interfaces))
	var result []NetInterface

	for i, iface := range interfaces {
		log.Printf("🌐 getNetworkInterfaces: 处理接口 %d/%d: %s", i+1, len(interfaces), iface.Name)

		netIface := NetInterface{
			Name:       iface.Name,
			MACAddress: iface.HardwareAddr.String(),
		}

		// 获取接口状态
		if iface.Flags&net.FlagUp != 0 {
			netIface.Status = "Up"
		} else {
			netIface.Status = "Down"
		}

		// 获取接口类型
		if iface.Flags&net.FlagLoopback != 0 {
			netIface.Type = "Loopback"
		} else if strings.HasPrefix(iface.Name, "en") {
			netIface.Type = "Ethernet"
		} else if strings.HasPrefix(iface.Name, "wlan") || strings.HasPrefix(iface.Name, "wifi") {
			netIface.Type = "Wireless"
		} else {
			netIface.Type = "Other"
		}

		// 获取IP地址
		addrs, err := iface.Addrs()
		if err == nil {
			for _, addr := range addrs {
				if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
					if ipnet.IP.To4() != nil {
						netIface.IPAddress = ipnet.IP.String()
						break
					}
				}
			}
		}

		// 从 netstat 获取统计信息
		cm.getDarwinInterfaceStats(&netIface)

		result = append(result, netIface)

		// 🚀 流式传输：每处理完一个接口就发送一次进度更新
		if i < len(interfaces)-1 { // 不是最后一个接口
			cm.sendInterfaceProgress(i+1, len(interfaces), netIface)
		}
	}

	log.Printf("✅ getNetworkInterfaces: 网络接口信息获取完成，共%d个接口", len(result))
	return result, nil
}

// getInterfaceStatsAlternative 备用的接口统计获取方法 - 尝试其他方式获取真实数据
func (cm *ConnectionManager) getInterfaceStatsAlternative(iface *NetInterface) {
	log.Printf("🔧 getInterfaceStatsAlternative: 尝试为接口%s获取真实统计数据", iface.Name)

	// 🚀 方法1：尝试使用ifconfig命令获取接口统计
	if cm.getStatsFromIfconfig(iface) {
		log.Printf("✅ getInterfaceStatsAlternative: 通过ifconfig成功获取接口%s统计", iface.Name)
		return
	}

	// 🚀 方法2：尝试读取系统文件获取统计（macOS特定路径）
	if cm.getStatsFromSystemFiles(iface) {
		log.Printf("✅ getInterfaceStatsAlternative: 通过系统文件成功获取接口%s统计", iface.Name)
		return
	}

	// 如果所有方法都失败，保持原始的0值（真实的无数据状态）
	log.Printf("⚠️ getInterfaceStatsAlternative: 无法获取接口%s的统计数据，保持0值", iface.Name)
}

// getStatsFromIfconfig 使用ifconfig命令获取真实的接口统计数据
func (cm *ConnectionManager) getStatsFromIfconfig(iface *NetInterface) bool {
	log.Printf("🔧 getStatsFromIfconfig: 尝试通过ifconfig获取接口%s统计", iface.Name)

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "ifconfig", iface.Name)
	output, err := cmd.Output()
	if err != nil {
		log.Printf("⚠️ getStatsFromIfconfig: ifconfig命令失败: %v", err)
		return false
	}

	outputStr := string(output)
	// 安全地截取输出用于日志显示
	logOutput := outputStr
	if len(outputStr) > 200 {
		logOutput = outputStr[:200] + "..."
	}
	log.Printf("📊 getStatsFromIfconfig: ifconfig输出: %s", logOutput)

	// 解析ifconfig输出获取统计数据
	lines := strings.Split(outputStr, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 查找包含统计信息的行，例如：
		// RX packets 1234 bytes 5678 (5.6 KB)
		// TX packets 9012 bytes 3456 (3.4 KB)
		if strings.Contains(line, "RX packets") {
			if bytes := cm.extractBytesFromLine(line); bytes > 0 {
				iface.BytesReceived = bytes
				log.Printf("✅ getStatsFromIfconfig: 接口%s接收字节数: %d", iface.Name, bytes)
			}
		} else if strings.Contains(line, "TX packets") {
			if bytes := cm.extractBytesFromLine(line); bytes > 0 {
				iface.BytesSent = bytes
				log.Printf("✅ getStatsFromIfconfig: 接口%s发送字节数: %d", iface.Name, bytes)
			}
		}
	}

	return iface.BytesSent > 0 || iface.BytesReceived > 0
}

// getStatsFromSystemFiles 从系统文件获取真实的接口统计数据
func (cm *ConnectionManager) getStatsFromSystemFiles(iface *NetInterface) bool {
	log.Printf("🔧 getStatsFromSystemFiles: 尝试通过系统文件获取接口%s统计", iface.Name)

	// macOS没有像Linux那样的/proc/net/dev文件
	// 但可以尝试其他系统调用或工具

	// 尝试使用netstat -ib命令获取字节统计
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "netstat", "-ib")
	output, err := cmd.Output()
	if err != nil {
		log.Printf("⚠️ getStatsFromSystemFiles: netstat -ib命令失败: %v", err)
		return false
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		// 🔧 修复：只匹配<Link#>行，避免匹配到IP地址行
		if strings.HasPrefix(line, iface.Name+" ") && strings.Contains(line, "<Link#") {
			fields := strings.Fields(line)
			if len(fields) >= 11 {
				// netstat -ib输出格式：Name Mtu Network Address Ipkts Ierrs Ibytes Opkts Oerrs Obytes Coll
				log.Printf("🔍 getStatsFromSystemFiles: 解析接口%s统计数据: %v", iface.Name, fields)

				if ibytes, err := strconv.ParseUint(fields[6], 10, 64); err == nil {
					iface.BytesReceived = ibytes
				}
				if obytes, err := strconv.ParseUint(fields[9], 10, 64); err == nil {
					iface.BytesSent = obytes
				}
				log.Printf("✅ getStatsFromSystemFiles: 接口%s统计 - 接收:%d, 发送:%d",
					iface.Name, iface.BytesReceived, iface.BytesSent)
				return true
			} else {
				log.Printf("⚠️ getStatsFromSystemFiles: 接口%s字段数不足: %d < 11", iface.Name, len(fields))
			}
		}
	}

	return false
}

// 网络统计缓存变量（用于计算速度）
var (
	lastNetworkStat      time.Time
	prevBytesSent        uint64
	prevBytesRecv        uint64
	isNetworkInitialized bool
	lastUploadKbps       float64
	lastDownloadKbps     float64
	// 注意：networkMutex已在上面声明，这里不重复声明
)

// getRealNetworkStats 获取真实的网络统计数据（字节数和速度）
func (cm *ConnectionManager) getRealNetworkStats() (uint64, uint64, float64, float64, error) {
	log.Printf("🔧 getRealNetworkStats: 开始获取真实网络统计")

	networkMutex.Lock()
	defer networkMutex.Unlock()

	now := time.Now()

	// 🚀 使用Go原生API获取网络统计，避免netstat命令被杀死
	currentSent, currentRecv, err := cm.getStatsFromGoNative()
	if err != nil {
		log.Printf("⚠️ getRealNetworkStats: 获取字节统计失败: %v", err)
		return 0, 0, 0, 0, err
	}

	// 首次初始化
	if !isNetworkInitialized {
		prevBytesSent = currentSent
		prevBytesRecv = currentRecv
		lastNetworkStat = now
		isNetworkInitialized = true
		log.Printf("🔧 getRealNetworkStats: 首次初始化，返回0速度（下次调用将显示真实速度）")
		return currentSent, currentRecv, 0, 0, nil
	}

	// 计算时间差（秒）
	duration := now.Sub(lastNetworkStat).Seconds()
	if duration < 0.5 { // 减少最小时间间隔到0.5秒，学习dashboard
		log.Printf("🔧 getRealNetworkStats: 时间间隔太短(%.2fs)，返回上次速度", duration)
		return currentSent, currentRecv, lastUploadKbps, lastDownloadKbps, nil
	}

	// 计算字节差值
	sentDiff := currentSent - prevBytesSent
	recvDiff := currentRecv - prevBytesRecv

	// 检测计数器重置（网络接口重启等情况）
	if currentSent < prevBytesSent || currentRecv < prevBytesRecv {
		log.Printf("⚠️ getRealNetworkStats: 检测到计数器重置，重新初始化")
		prevBytesSent = currentSent
		prevBytesRecv = currentRecv
		lastNetworkStat = now
		return currentSent, currentRecv, 0, 0, nil
	}

	// 🚀 计算真实速率（KB/s）- 学习dashboard的计算方法
	uploadKbps := float64(sentDiff) / duration / 1024
	downloadKbps := float64(recvDiff) / duration / 1024

	log.Printf("📊 getRealNetworkStats: 时间间隔=%.2fs, 发送差值=%d, 接收差值=%d", duration, sentDiff, recvDiff)
	log.Printf("📊 getRealNetworkStats: 计算速度 - 上传=%.1fKB/s, 下载=%.1fKB/s", uploadKbps, downloadKbps)

	// 更新缓存变量
	prevBytesSent = currentSent
	prevBytesRecv = currentRecv
	lastNetworkStat = now
	lastUploadKbps = uploadKbps
	lastDownloadKbps = downloadKbps

	return currentSent, currentRecv, uploadKbps, downloadKbps, nil
}

// getStatsFromNetstatIb 使用netstat -ib获取真实的字节统计
func (cm *ConnectionManager) getStatsFromNetstatIb() (uint64, uint64, error) {
	log.Printf("🔧 getStatsFromNetstatIb: 使用netstat -ib获取字节统计")

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "netstat", "-ib")
	output, err := cmd.Output()
	if err != nil {
		log.Printf("⚠️ getStatsFromNetstatIb: netstat -ib命令失败: %v", err)
		return 0, 0, err
	}

	var totalBytesSent, totalBytesRecv uint64
	lines := strings.Split(string(output), "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "Name") {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) >= 10 {
			// netstat -ib输出格式：Name Mtu Network Address Ipkts Ierrs Ibytes Opkts Oerrs Obytes Coll
			// 跳过回环接口
			if len(fields) > 0 && (fields[0] == "lo0" || strings.HasPrefix(fields[0], "lo")) {
				continue
			}

			// 解析接收字节数 (Ibytes)
			if ibytes, err := strconv.ParseUint(fields[6], 10, 64); err == nil {
				totalBytesRecv += ibytes
			}
			// 解析发送字节数 (Obytes)
			if obytes, err := strconv.ParseUint(fields[9], 10, 64); err == nil {
				totalBytesSent += obytes
			}
		}
	}

	log.Printf("✅ getStatsFromNetstatIb: 总发送字节=%d, 总接收字节=%d", totalBytesSent, totalBytesRecv)
	return totalBytesSent, totalBytesRecv, nil
}

// getStatsFromGoNative 使用Go原生API获取网络统计（避免netstat命令被杀死）
func (cm *ConnectionManager) getStatsFromGoNative() (uint64, uint64, error) {
	log.Printf("🔧 getStatsFromGoNative: 使用Go原生API获取网络统计")

	// 🚀 方法1：优先使用gopsutil库（学习dashboard实现）
	if totalSent, totalRecv, err := cm.getStatsFromPsutil(); err == nil {
		log.Printf("✅ getStatsFromGoNative: 通过gopsutil获取成功")
		return totalSent, totalRecv, nil
	} else {
		log.Printf("⚠️ getStatsFromGoNative: gopsutil方法失败: %v", err)
	}

	// 🚀 方法2：使用Go标准库遍历网络接口
	totalSent, totalRecv, err := cm.getStatsFromGoStdlib()
	if err != nil {
		log.Printf("⚠️ getStatsFromGoNative: Go标准库方法失败: %v", err)
		return 0, 0, err
	}

	log.Printf("✅ getStatsFromGoNative: 通过Go标准库获取成功")
	return totalSent, totalRecv, nil
}

// getStatsFromPsutil 使用gopsutil库获取网络统计（修复版本：包含所有接口流量）
func (cm *ConnectionManager) getStatsFromPsutil() (uint64, uint64, error) {
	log.Printf("🔧 getStatsFromPsutil: 使用gopsutil获取网络统计")

	// 🚀 获取所有网络接口的统计信息
	stats, err := psutilNet.IOCounters(true) // 获取每个接口的详细信息
	if err != nil || len(stats) == 0 {
		return 0, 0, fmt.Errorf("gopsutil获取网络统计失败: %v", err)
	}

	log.Printf("📊 getStatsFromPsutil: 找到%d个网络接口", len(stats))

	// 🚀 修复：汇总所有接口的流量（除了回环接口），不再过滤虚拟接口
	// 这样可以确保包含所有真实的网络流量，包括虚拟机、桥接等产生的流量
	var totalSent, totalRecv uint64
	var validInterfaceCount int

	for _, stat := range stats {
		// 只排除回环接口，保留所有其他接口（包括虚拟接口）
		if stat.Name == "lo" || stat.Name == "Loopback" || strings.HasPrefix(stat.Name, "lo") {
			log.Printf("🚫 getStatsFromPsutil: 跳过回环接口 %s", stat.Name)
			continue
		}

		totalSent += stat.BytesSent
		totalRecv += stat.BytesRecv
		validInterfaceCount++

		log.Printf("📊 getStatsFromPsutil: 包含接口 %s: 发送=%d, 接收=%d",
			stat.Name, stat.BytesSent, stat.BytesRecv)
	}

	log.Printf("✅ getStatsFromPsutil: 汇总%d个接口的统计 - 总发送=%d, 总接收=%d",
		validInterfaceCount, totalSent, totalRecv)

	// 🚀 确保返回真实数据，即使是0也是有效的
	return totalSent, totalRecv, nil
}

// isValidNetworkInterface 检查是否为有效的网络接口（完全学习dashboard实现）
func (cm *ConnectionManager) isValidNetworkInterface(name string) bool {
	// 🚀 完全学习dashboard的接口过滤逻辑
	excludedPrefixes := []string{
		"lo",       // 回环接口
		"Loopback", // Windows回环接口
		"utun",     // macOS隧道接口
		"awdl",     // Apple Wireless Direct Link
		"llw",      // Low Latency WLAN
		"bridge",   // 桥接接口
		"vmenet",   // VMware虚拟网络接口
		"vnic",     // 虚拟网卡
		"docker",   // Docker接口
		"veth",     // 虚拟以太网接口
		"tap",      // TAP接口
		"tun",      // TUN接口
		"ap",       // 接入点接口
		"anpi",     // Apple Network Processing Interface
	}

	for _, prefix := range excludedPrefixes {
		if strings.HasPrefix(strings.ToLower(name), strings.ToLower(prefix)) {
			return false
		}
	}

	return true
}

// getStatsFromGoStdlib 使用Go标准库获取网络统计（基础方法）
func (cm *ConnectionManager) getStatsFromGoStdlib() (uint64, uint64, error) {
	log.Printf("🔧 getStatsFromGoStdlib: 使用Go标准库获取网络统计")

	interfaces, err := net.Interfaces()
	if err != nil {
		return 0, 0, fmt.Errorf("获取网络接口失败: %v", err)
	}

	var totalSent, totalRecv uint64

	// 注意：Go标准库的net包无法直接获取字节统计
	// 这里我们只能获取接口信息，无法获取流量统计
	// 但我们可以尝试读取系统文件

	for _, iface := range interfaces {
		// 跳过回环接口
		if iface.Flags&net.FlagLoopback != 0 {
			continue
		}

		// 尝试从系统文件读取统计（macOS可能没有/proc/net/dev）
		sent, recv := cm.getInterfaceStatsFromSystem(iface.Name)
		totalSent += sent
		totalRecv += recv
	}

	log.Printf("📊 getStatsFromGoStdlib: 总发送=%d, 总接收=%d", totalSent, totalRecv)
	return totalSent, totalRecv, nil
}

// getInterfaceStatsFromSystem 从系统获取单个接口的统计（macOS特定）
func (cm *ConnectionManager) getInterfaceStatsFromSystem(ifaceName string) (uint64, uint64) {
	// 🚀 尝试使用更轻量的系统命令，避免被杀死

	// 方法1：尝试使用route命令获取接口统计
	if sent, recv, err := cm.getStatsFromRoute(ifaceName); err == nil {
		return sent, recv
	}

	// 方法2：尝试读取系统信息文件
	if sent, recv, err := cm.getStatsFromSysctl(ifaceName); err == nil {
		return sent, recv
	}

	// 如果所有方法都失败，返回0
	return 0, 0
}

// getStatsFromRoute 使用route命令获取接口统计
func (cm *ConnectionManager) getStatsFromRoute(ifaceName string) (uint64, uint64, error) {
	// route命令通常比netstat更轻量，不容易被杀死
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "route", "get", "default")
	output, err := cmd.Output()
	if err != nil {
		return 0, 0, fmt.Errorf("route命令失败: %v", err)
	}

	// route命令主要用于路由信息，不包含详细的字节统计
	// 这里只是一个尝试，实际可能无法获取到有用信息
	_ = output
	return 0, 0, fmt.Errorf("route命令不包含字节统计")
}

// getStatsFromSysctl 使用sysctl获取网络统计
func (cm *ConnectionManager) getStatsFromSysctl(ifaceName string) (uint64, uint64, error) {
	// macOS可以使用sysctl获取一些网络统计
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// 尝试获取网络接口统计
	cmd := exec.CommandContext(ctx, "sysctl", "-n", "net.link.generic.system")
	output, err := cmd.Output()
	if err != nil {
		return 0, 0, fmt.Errorf("sysctl命令失败: %v", err)
	}

	// sysctl的输出格式复杂，需要解析
	// 这里只是一个基础实现
	_ = output
	return 0, 0, fmt.Errorf("sysctl解析暂未实现")
}

// extractBytesFromLine 从ifconfig输出行中提取字节数
func (cm *ConnectionManager) extractBytesFromLine(line string) uint64 {
	// 查找 "bytes" 关键字后的数字
	parts := strings.Fields(line)
	for i, part := range parts {
		if part == "bytes" && i > 0 {
			if bytes, err := strconv.ParseUint(parts[i-1], 10, 64); err == nil {
				return bytes
			}
		}
	}
	return 0
}

// sendInterfaceProgress 发送接口处理进度
func (cm *ConnectionManager) sendInterfaceProgress(current, total int, iface NetInterface) {
	log.Printf("📤 发送接口进度: %d/%d - %s", current, total, iface.Name)

	progressData := map[string]interface{}{
		"type":      "interface_progress",
		"current":   current,
		"total":     total,
		"progress":  float64(current) / float64(total) * 100,
		"interface": iface,
		"message":   fmt.Sprintf("正在处理接口 %s (%d/%d)", iface.Name, current, total),
	}

	// 发送进度数据包（使用专门的进度Code）
	cm.sendProgressResponse(NetProgressCmd, progressData)
}

// sendProgressResponse 发送进度响应
func (cm *ConnectionManager) sendProgressResponse(code uint8, data interface{}) {
	log.Printf("📤 sendProgressResponse: 发送进度数据")

	// 添加调试日志：打印即将发送的进度数据
	if jsonData, err := json.Marshal(data); err == nil {
		log.Printf("📤 sendProgressResponse: 进度数据JSON: %s", string(jsonData))
	} else {
		log.Printf("❌ 序列化进度数据失败: %v", err)
		return
	}

	log.Printf("📤 sendProgressResponse: 调用 sendResp...")
	// 🔧 修复：直接传递原始数据，让sendResp进行序列化，避免双重序列化
	cm.sendResp(Network, code, data)
}

// getDarwinInterfaceStats 使用 gopsutil 获取接口统计信息并计算实时速度
func (cm *ConnectionManager) getDarwinInterfaceStats(iface *NetInterface) {
	// 🚀 直接使用 gopsutil 获取网络接口统计信息
	ioCounters, err := psutilNet.IOCounters(true) // true表示获取每个接口的统计
	if err != nil {
		log.Printf("⚠️ getDarwinInterfaceStats: gopsutil获取接口统计失败: %v", err)
		return
	}

	// 查找对应的接口
	for _, counter := range ioCounters {
		if counter.Name == iface.Name {
			// 直接从 gopsutil 获取真实的流量统计数据
			iface.BytesSent = counter.BytesSent
			iface.BytesReceived = counter.BytesRecv
			iface.PacketsSent = counter.PacketsSent
			iface.PacketsRecv = counter.PacketsRecv
			iface.ErrorsIn = counter.Errin
			iface.ErrorsOut = counter.Errout

			log.Printf("✅ getDarwinInterfaceStats: 接口%s统计 - 发送:%d字节, 接收:%d字节",
				iface.Name, iface.BytesSent, iface.BytesReceived)

			// 🚀 计算实时速度
			cm.calculateInterfaceSpeedDarwin(iface)
			return
		}
	}

	log.Printf("⚠️ getDarwinInterfaceStats: 未找到接口%s的统计信息", iface.Name)
}

// getNetworkConnections 获取网络连接信息
func (cm *ConnectionManager) getNetworkConnections(protocol, state string) ([]NetworkConnection, error) {
	var connections []NetworkConnection

	log.Printf("🔍 开始获取网络连接信息: protocol=%s, state=%s", protocol, state)

	// 根据协议类型获取连接
	if protocol == "ALL" || protocol == "TCP" || protocol == "" {
		tcpConns, err := cm.getTCPConnections()
		if err == nil {
			connections = append(connections, tcpConns...)
			log.Printf("📊 获取到 %d 个TCP连接", len(tcpConns))
		} else {
			log.Printf("❌ 获取TCP连接失败: %v", err)
		}
	}

	if protocol == "ALL" || protocol == "UDP" {
		udpConns, err := cm.getUDPConnections()
		if err == nil {
			connections = append(connections, udpConns...)
			log.Printf("📊 获取到 %d 个UDP连接", len(udpConns))
		} else {
			log.Printf("❌ 获取UDP连接失败: %v", err)
		}
	}

	// 根据状态过滤
	if state != "" {
		var filtered []NetworkConnection
		for _, conn := range connections {
			if conn.State == state {
				filtered = append(filtered, conn)
			}
		}
		connections = filtered
		log.Printf("📊 状态过滤后剩余 %d 个连接", len(connections))
	}

	// 限制返回的连接数量，防止数据过大
	if len(connections) > MaxNetworkConnections {
		log.Printf("⚠️ 连接数量 %d 超过限制 %d，将截取前 %d 个连接",
			len(connections), MaxNetworkConnections, MaxNetworkConnections)
		connections = connections[:MaxNetworkConnections]
	}

	log.Printf("✅ 最终返回 %d 个网络连接", len(connections))
	return connections, nil
}

// getTCPConnections 获取TCP连接（使用lsof获取PID信息）
func (cm *ConnectionManager) getTCPConnections() ([]NetworkConnection, error) {
	// 🚀 优先使用lsof命令获取包含PID的连接信息
	connections, err := cm.getConnectionsWithLsof("tcp")
	if err == nil && len(connections) > 0 {
		return connections, nil
	}

	log.Printf("⚠️ lsof方法失败，回退到netstat: %v", err)
	// 备用方法：使用netstat（但PID会是0）
	cmd := exec.Command("netstat", "-an", "-p", "tcp")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行netstat命令失败: %v", err)
	}

	return cm.parseNetstatOutput(string(output), "TCP"), nil
}

// getUDPConnections 获取UDP连接（使用lsof获取PID信息）
func (cm *ConnectionManager) getUDPConnections() ([]NetworkConnection, error) {
	// 🚀 优先使用lsof命令获取包含PID的连接信息
	connections, err := cm.getConnectionsWithLsof("udp")
	if err == nil && len(connections) > 0 {
		return connections, nil
	}

	log.Printf("⚠️ lsof方法失败，回退到netstat: %v", err)
	// 备用方法：使用netstat（但PID会是0）
	cmd := exec.Command("netstat", "-an", "-p", "udp")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行netstat命令失败: %v", err)
	}

	return cm.parseNetstatOutput(string(output), "UDP"), nil
}

// parseNetstatOutput 解析netstat输出
func (cm *ConnectionManager) parseNetstatOutput(output, protocol string) []NetworkConnection {
	var connections []NetworkConnection
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if !strings.HasPrefix(strings.ToLower(line), strings.ToLower(protocol)) {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) < 4 {
			continue
		}

		conn := NetworkConnection{
			Protocol:        protocol,
			EstablishedTime: time.Now(),
		}

		// 解析本地地址
		if addr, port, err := cm.parseDarwinAddress(parts[3]); err == nil {
			conn.LocalAddress = addr
			conn.LocalPort = port
		}

		// 解析远程地址
		if len(parts) > 4 && parts[4] != "*.*" {
			if addr, port, err := cm.parseDarwinAddress(parts[4]); err == nil {
				conn.RemoteAddress = addr
				conn.RemotePort = port
			}
		}

		// 解析状态
		if len(parts) > 5 {
			conn.State = parts[5]
		} else if protocol == "UDP" {
			conn.State = "ESTABLISHED"
		}

		// 生成连接ID
		conn.ID = fmt.Sprintf("%s_%s:%d_%s:%d", protocol, conn.LocalAddress, conn.LocalPort, conn.RemoteAddress, conn.RemotePort)

		connections = append(connections, conn)
	}

	return connections
}

// parseDarwinAddress 解析Darwin地址格式
func (cm *ConnectionManager) parseDarwinAddress(addrStr string) (string, int, error) {
	// Darwin netstat 输出格式可能是 ip.port 或 ip:port
	var addr string
	var portStr string

	if strings.Contains(addrStr, ":") {
		parts := strings.Split(addrStr, ":")
		if len(parts) != 2 {
			return "", 0, fmt.Errorf("无效的地址格式: %s", addrStr)
		}
		addr = parts[0]
		portStr = parts[1]
	} else if strings.Contains(addrStr, ".") {
		// 查找最后一个点，分离IP和端口
		lastDot := strings.LastIndex(addrStr, ".")
		if lastDot == -1 {
			return "", 0, fmt.Errorf("无效的地址格式: %s", addrStr)
		}
		addr = addrStr[:lastDot]
		portStr = addrStr[lastDot+1:]
	} else {
		return "", 0, fmt.Errorf("无效的地址格式: %s", addrStr)
	}

	if addr == "0.0.0.0" || addr == "*" {
		addr = "127.0.0.1"
	}

	port, err := strconv.Atoi(portStr)
	if err != nil {
		return "", 0, fmt.Errorf("解析端口失败: %v", err)
	}

	return addr, port, nil
}

// closeNetworkConnection 关闭网络连接
func (cm *ConnectionManager) closeNetworkConnection(connectionID, protocol, localAddr string, localPort int, remoteAddr string, remotePort int) error {
	log.Printf("尝试关闭连接: %s %s:%d -> %s:%d", protocol, localAddr, localPort, remoteAddr, remotePort)

	// 检查是否有管理员权限
	if !cm.isAdmin() {
		return fmt.Errorf("macOS连接关闭功能需要管理员权限，当前不支持")
	}

	// 第一层：尝试使用lsof+kill方法关闭连接
	err := cm.closeConnectionWithLsof(protocol, localAddr, localPort, remoteAddr, remotePort)
	if err == nil {
		log.Printf("✅ 使用lsof+kill方法成功关闭连接")
		return nil
	}
	log.Printf("⚠️ lsof+kill方法关闭连接失败: %v，尝试netstat+kill方法", err)

	// 第二层：尝试使用netstat+kill方法关闭连接
	err = cm.closeConnectionWithNetstat(protocol, localAddr, localPort, remoteAddr, remotePort)
	if err == nil {
		log.Printf("✅ 使用netstat+kill方法成功关闭连接")
		return nil
	}

	log.Printf("❌ netstat+kill方法也失败: %v", err)
	return fmt.Errorf("关闭连接失败，已尝试lsof和netstat方法: %v", err)
}

// isAdmin 检查当前进程是否具有管理员权限
func (cm *ConnectionManager) isAdmin() bool {
	// 在macOS中，检查有效用户ID是否为0（root）
	return os.Geteuid() == 0
}

// closeConnectionWithLsof 使用lsof+kill方法关闭连接
func (cm *ConnectionManager) closeConnectionWithLsof(protocol, localAddr string, localPort int, remoteAddr string, remotePort int) error {
	// 使用lsof查找占用该端口的进程
	cmd := exec.Command("lsof", "-i", fmt.Sprintf("%s:%d", strings.ToLower(protocol), localPort))
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("执行lsof命令失败: %v", err)
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, fmt.Sprintf(":%d", localPort)) {
			// 提取PID
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				pidStr := parts[1]
				if pid, err := strconv.Atoi(pidStr); err == nil {
					// 发送SIGTERM信号终止进程
					cmd := exec.Command("kill", "-TERM", fmt.Sprintf("%d", pid))
					if err := cmd.Run(); err != nil {
						// 如果SIGTERM失败，尝试SIGKILL
						cmd = exec.Command("kill", "-KILL", fmt.Sprintf("%d", pid))
						return cmd.Run()
					}
					return nil
				}
			}
		}
	}

	return fmt.Errorf("未找到占用端口的进程")
}

// closeConnectionWithNetstat 使用netstat+kill方法关闭连接
func (cm *ConnectionManager) closeConnectionWithNetstat(protocol, localAddr string, localPort int, remoteAddr string, remotePort int) error {
	// 使用netstat查找占用该连接的进程（macOS的netstat不直接显示PID）
	// 这里使用ps命令配合netstat的结果
	cmd := exec.Command("netstat", "-an", "-p", strings.ToLower(protocol))
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("执行netstat命令失败: %v", err)
	}

	// 检查连接是否存在
	lines := strings.Split(string(output), "\n")
	connectionExists := false
	for _, line := range lines {
		if strings.Contains(line, fmt.Sprintf("%s.%d", localAddr, localPort)) &&
			strings.Contains(line, fmt.Sprintf("%s.%d", remoteAddr, remotePort)) {
			connectionExists = true
			break
		}
	}

	if !connectionExists {
		return fmt.Errorf("未找到指定的网络连接")
	}

	// 使用lsof作为备用方法查找进程
	return cm.closeConnectionWithLsof(protocol, localAddr, localPort, remoteAddr, remotePort)
}

// calculateInterfaceSpeedDarwin 计算Darwin网络接口的实时速度
func (cm *ConnectionManager) calculateInterfaceSpeedDarwin(iface *NetInterface) {
	now := time.Now()

	// 获取或创建缓存
	cm.interfaceCacheMutex.Lock()
	cache, exists := cm.interfaceSpeedCache[iface.Name]
	if !exists {
		// 首次采样，创建缓存但不计算速度
		cache = &InterfaceSpeedCache{
			LastBytesSent:     iface.BytesSent,
			LastBytesReceived: iface.BytesReceived,
			LastTimestamp:     now,
			CurrentUpSpeed:    0,
			CurrentDownSpeed:  0,
		}
		cm.interfaceSpeedCache[iface.Name] = cache
		cm.interfaceCacheMutex.Unlock()

		// 首次采样，速度设为0
		iface.Speed = 0
		return
	}
	cm.interfaceCacheMutex.Unlock()

	// 计算时间差（秒）
	timeDiff := now.Sub(cache.LastTimestamp).Seconds()
	if timeDiff < 0.1 { // 避免时间间隔太短导致的计算误差
		// 使用缓存的速度值
		iface.Speed = uint64(cache.CurrentUpSpeed + cache.CurrentDownSpeed)
		return
	}

	// 计算字节差
	bytesSentDiff := int64(iface.BytesSent) - int64(cache.LastBytesSent)
	bytesRecvDiff := int64(iface.BytesReceived) - int64(cache.LastBytesReceived)

	// 处理计数器重置的情况（通常不会发生，但为了健壮性）
	if bytesSentDiff < 0 {
		bytesSentDiff = 0
	}
	if bytesRecvDiff < 0 {
		bytesRecvDiff = 0
	}

	// 计算速度 (字节/秒)
	upSpeed := float64(bytesSentDiff) / timeDiff
	downSpeed := float64(bytesRecvDiff) / timeDiff

	// 更新缓存
	cm.interfaceCacheMutex.Lock()
	cache.LastBytesSent = iface.BytesSent
	cache.LastBytesReceived = iface.BytesReceived
	cache.LastTimestamp = now
	cache.CurrentUpSpeed = upSpeed
	cache.CurrentDownSpeed = downSpeed
	cm.interfaceCacheMutex.Unlock()

	// 设置接口速度（总速度 = 上传 + 下载）
	totalSpeed := upSpeed + downSpeed
	if totalSpeed < 0 {
		totalSpeed = 0
	}
	iface.Speed = uint64(totalSpeed)

	log.Printf("🚀 Darwin接口 %s 速度计算: 上传=%.2f B/s, 下载=%.2f B/s, 总计=%.2f B/s",
		iface.Name, upSpeed, downSpeed, totalSpeed)
}

// getConnectionsWithLsof 使用lsof命令获取包含PID的网络连接信息
func (cm *ConnectionManager) getConnectionsWithLsof(protocol string) ([]NetworkConnection, error) {
	log.Printf("🔍 使用lsof命令获取%s连接信息", protocol)

	// 构建lsof命令参数
	// -i: 显示网络连接
	// -n: 不解析主机名
	// -P: 不解析端口名
	// -F: 指定输出格式
	args := []string{"-i", "-n", "-P", "-F", "pcn"}

	// 根据协议过滤
	if protocol == "tcp" {
		args = []string{"-i", "tcp", "-n", "-P", "-F", "pcn"}
	} else if protocol == "udp" {
		args = []string{"-i", "udp", "-n", "-P", "-F", "pcn"}
	}

	// 执行lsof命令
	cmd := exec.Command("lsof", args...)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行lsof命令失败: %v", err)
	}

	return cm.parseLsofOutput(string(output), protocol), nil
}

// parseLsofOutput 解析lsof命令的输出
func (cm *ConnectionManager) parseLsofOutput(output, protocol string) []NetworkConnection {
	var connections []NetworkConnection
	lines := strings.Split(output, "\n")

	var currentPID int
	var currentProcessName string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// lsof -F输出格式：
		// p<PID>
		// c<command>
		// n<network_address>

		if strings.HasPrefix(line, "p") {
			// 新的进程开始
			if pidStr := line[1:]; pidStr != "" {
				if pid, err := strconv.Atoi(pidStr); err == nil {
					currentPID = pid
				}
			}
		} else if strings.HasPrefix(line, "c") {
			// 进程名
			currentProcessName = line[1:]
		} else if strings.HasPrefix(line, "n") {
			// 网络地址
			networkAddr := line[1:]
			if conn := cm.parseLsofNetworkAddress(networkAddr, protocol); conn != nil {
				conn.PID = currentPID
				conn.ProcessName = currentProcessName
				if conn.ProcessName == "" && currentPID > 0 {
					// 🚀 利用现有进程缓存获取进程名
					conn.ProcessName = cm.getProcessNameFromCacheDarwin(currentPID)
				}
				connections = append(connections, *conn)
			}
		}
	}

	log.Printf("📊 lsof命令解析完成，获取到 %d 个%s连接", len(connections), protocol)
	return connections
}

// parseLsofNetworkAddress 解析lsof输出的网络地址
func (cm *ConnectionManager) parseLsofNetworkAddress(networkAddr, protocol string) *NetworkConnection {
	// lsof网络地址格式示例：
	// TCP: *************:22->*************:54321 (ESTABLISHED)
	// UDP: *:53 (UDP)
	// IPv6: [::1]:22->[::1]:54321 (ESTABLISHED)

	if !strings.Contains(networkAddr, ":") {
		return nil // 无效的网络地址
	}

	conn := &NetworkConnection{
		Protocol:        strings.ToUpper(protocol),
		EstablishedTime: time.Now(),
	}

	// 提取状态信息
	if parenIndex := strings.Index(networkAddr, "("); parenIndex != -1 {
		if closeParenIndex := strings.Index(networkAddr[parenIndex:], ")"); closeParenIndex != -1 {
			state := networkAddr[parenIndex+1 : parenIndex+closeParenIndex]
			conn.State = state
			networkAddr = strings.TrimSpace(networkAddr[:parenIndex])
		}
	}

	// 解析地址对
	if strings.Contains(networkAddr, "->") {
		// TCP连接格式: local->remote
		parts := strings.Split(networkAddr, "->")
		if len(parts) == 2 {
			if addr, port, err := cm.parseDarwinAddress(parts[0]); err == nil {
				conn.LocalAddress = addr
				conn.LocalPort = port
			}
			if addr, port, err := cm.parseDarwinAddress(parts[1]); err == nil {
				conn.RemoteAddress = addr
				conn.RemotePort = port
			}
		}
	} else {
		// UDP监听格式: local
		if addr, port, err := cm.parseDarwinAddress(networkAddr); err == nil {
			conn.LocalAddress = addr
			conn.LocalPort = port
			conn.RemoteAddress = "0.0.0.0"
			conn.RemotePort = 0
		}
	}

	// 生成连接ID
	conn.ID = fmt.Sprintf("%s_%s:%d_%s:%d", conn.Protocol, conn.LocalAddress, conn.LocalPort, conn.RemoteAddress, conn.RemotePort)

	return conn
}

// getProcessNameFromCacheDarwin 从Darwin进程缓存中获取进程名
func (cm *ConnectionManager) getProcessNameFromCacheDarwin(pid int) string {
	// 🚀 利用现有的进程缓存机制
	processCacheMutex.RLock()
	if cachedInfo, exists := processInfoCache[int32(pid)]; exists {
		processCacheMutex.RUnlock()
		return cachedInfo.Name
	}
	processCacheMutex.RUnlock()

	// 缓存未命中，异步获取进程信息并缓存
	go func() {
		if p, err := process.NewProcess(int32(pid)); err == nil {
			if name, err := p.Name(); err == nil && name != "" {
				// 简单缓存进程名（不使用完整的进程信息结构）
				log.Printf("🔄 Darwin异步缓存进程名: PID=%d, Name=%s", pid, name)
			}
		}
	}()

	// 返回临时的进程名格式
	return fmt.Sprintf("PID:%d", pid)
}
