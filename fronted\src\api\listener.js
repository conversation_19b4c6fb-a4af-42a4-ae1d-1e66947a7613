import { get, post, put, del } from '@/utils/request'

/**
 * 获取监听器列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getListenerList(params) {
  return post('/listener/list', params)
}

/**
 * 获取单个监听器
 * @param {Number} id 监听器ID
 * @returns {Promise}
 */
export function getListener(id) {
  return get(`/listener/${id}`)
}

/**
 * 创建监听器
 * @param {Object} data 监听器数据
 * @returns {Promise}
 */
export function createListener(data) {
  return post('/listener', data)
}

/**
 * 更新监听器
 * @param {Object} data 监听器数据
 * @returns {Promise}
 */
export function updateListener(data) {
  return put('/listener', data)
}

/**
 * 删除监听器
 * @param {Number} id 监听器ID
 * @returns {Promise}
 */
export function deleteListener(id) {
  return del(`/listener/${id}`)
}

/**
 * 更新监听器状态
 * @param {Object} data 状态数据 {id, status}
 * @returns {Promise}
 */
export function updateListenerStatus(data) {
  return put('/listener/status', data)
}