package sys

import (
	"server/api/sys"

	"github.com/gin-gonic/gin"
)

type FileTransferStatsRouter struct{}

// InitFileTransferStatsRouter 初始化文件传输统计路由
func (f *FileTransferStatsRouter) InitFileTransferStatsRouter(Router *gin.RouterGroup) {
	fileTransferStatsRouter := Router.Group("file-transfer-stats")
	fileTransferStatsApi := sys.FileTransferStatsApi{}
	{
		fileTransferStatsRouter.GET("stats", fileTransferStatsApi.GetFileTransferStats)           // 获取文件传输统计信息
		fileTransferStatsRouter.POST("reset", fileTransferStatsApi.ResetFileTransferStats)       // 重置统计信息
		fileTransferStatsRouter.GET("pool-status", fileTransferStatsApi.GetFileTransferPoolStatus) // 获取内存池状态
	}
}
