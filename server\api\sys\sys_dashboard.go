package sys

import (
	"context"
	"server/core/shutdown"
	"server/global"
	"server/model/response"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DashboardApi struct{}

// GetSystemInfo 获取系统信息
func (i *DashboardApi) GetSystemInfo(c *gin.Context) {
	server, err := dashboardService.GetSystemInfo()
	if err != nil {
		global.LOG.Error("获取系统信息失败", zap.Error(err))
		response.ErrorWithMessage("获取系统信息失败", c)
		return
	}
	response.OkWithDetailed(gin.H{"server": server}, "获取成功", c)
}

// GetDashboardStats 获取dashboard统计信息
func (i *DashboardApi) GetDashboardStats(c *gin.Context) {
	stats, err := dashboardService.GetDashboardStats()
	if err != nil {
		global.LOG.Error("获取dashboard统计信息失败", zap.Error(err))
		response.ErrorWithMessage("获取dashboard统计信息失败", c)
		return
	}
	response.OkWithDetailed(gin.H{"stats": stats}, "获取成功", c)
}

// 🚀 新增：Dashboard SSE实时数据流
func (i *DashboardApi) DashboardSSE(c *gin.Context) {
	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 创建一个超时上下文，防止连接无限期阻塞
	timeoutCtx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Minute)
	defer cancel()

	// 🚀 注册goroutine到关闭管理器
	shutdown.RegisterGoroutine()
	defer shutdown.UnregisterGoroutine()

	// 创建定时器
	systemTicker := time.NewTicker(1 * time.Second)   // 系统信息1秒更新
	statsTicker := time.NewTicker(2 * time.Second)    // 统计信息2秒更新
	topologyTicker := time.NewTicker(5 * time.Second) // 拓扑数据5秒更新
	defer systemTicker.Stop()
	defer statsTicker.Stop()
	defer topologyTicker.Stop()

	// 立即发送一次数据
	i.sendSystemInfo(c)
	i.sendDashboardStats(c)
	i.sendTopologyData(c)

	global.LOG.Info("Dashboard SSE连接已建立")

	for {
		select {
		case <-timeoutCtx.Done():
			global.LOG.Info("Dashboard SSE连接超时或被取消")
			return
		case <-c.Request.Context().Done():
			global.LOG.Info("Dashboard SSE连接已断开")
			return
		case <-shutdown.GetShutdownChannel():
			global.LOG.Info("收到服务器关闭信号，停止Dashboard SSE推送")
			return
		case <-systemTicker.C:
			// 发送系统信息
			i.sendSystemInfo(c)
		case <-statsTicker.C:
			// 发送统计信息
			i.sendDashboardStats(c)
		case <-topologyTicker.C:
			// 发送拓扑数据
			i.sendTopologyData(c)
		}
	}
}

// 发送系统信息
func (i *DashboardApi) sendSystemInfo(c *gin.Context) {
	server, err := dashboardService.GetSystemInfo()
	if err != nil {
		global.LOG.Error("获取系统信息失败", zap.Error(err))
		return
	}

	// 发送SSE数据
	c.SSEvent("system_info", gin.H{
		"type":      "system_info",
		"data":      server,
		"timestamp": time.Now().Unix(),
	})
	c.Writer.Flush()
}

// 发送统计信息
func (i *DashboardApi) sendDashboardStats(c *gin.Context) {
	stats, err := dashboardService.GetDashboardStats()
	if err != nil {
		global.LOG.Error("获取dashboard统计信息失败", zap.Error(err))
		return
	}

	// 发送SSE数据
	c.SSEvent("dashboard_stats", gin.H{
		"type":      "dashboard_stats",
		"data":      stats,
		"timestamp": time.Now().Unix(),
	})
	c.Writer.Flush()
}

// 🌐 新增：获取网络拓扑图数据
func (i *DashboardApi) GetNetworkTopology(c *gin.Context) {
	topology, err := dashboardService.GetNetworkTopology()
	if err != nil {
		global.LOG.Error("获取网络拓扑数据失败", zap.Error(err))
		response.ErrorWithMessage("获取网络拓扑数据失败", c)
		return
	}
	response.OkWithDetailed(gin.H{"topology": topology}, "获取成功", c)
}

// 发送拓扑数据
func (i *DashboardApi) sendTopologyData(c *gin.Context) {
	topology, err := dashboardService.GetNetworkTopology()
	if err != nil {
		global.LOG.Error("获取拓扑数据失败", zap.Error(err))
		return
	}

	// 发送SSE数据
	c.SSEvent("topology_data", gin.H{
		"type": "topology_data",
		"data": topology,
		"timestamp": time.Now().Unix(),
	})
	c.Writer.Flush()
}
