package c2

import (
	"errors"
	"server/core/dbpool"
	"server/global"
	"server/model/request"
	"server/model/sys"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type HeartbeatConfigService struct{}

// GetHeartbeatConfig 获取心跳配置
func (h *HeartbeatConfigService) GetHeartbeatConfig(clientID uint) (*sys.HeartbeatConfig, error) {
	return sys.GetClientConfig(clientID)
}

// UpdateHeartbeatConfig 更新心跳配置
func (h *HeartbeatConfigService) UpdateHeartbeatConfig(config *sys.HeartbeatConfig) error {
	if config.ID == 0 {
		return errors.New("配置ID不能为空")
	}

	// 检查配置是否存在
	var existingConfig sys.HeartbeatConfig
	// 🚀 使用数据库连接池查询现有配置
	err := dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_query", func(db *gorm.DB) error {
		return db.Where("id = ?", config.ID).First(&existingConfig).Error
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("配置不存在")
		}
		return err
	}

	// 🚀 更新配置（不更新is_active，由服务器管理）
	return dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_update", func(db *gorm.DB) error {
		return db.Model(&existingConfig).Updates(map[string]interface{}{
			"interval":     config.Interval,
			"timeout":      config.Timeout,
			"max_retries":  config.MaxRetries,
			"jitter_range": config.JitterRange,
			"remark":       config.Remark,
		}).Error
	})
}

// CreateHeartbeatConfig 创建心跳配置
func (h *HeartbeatConfigService) CreateHeartbeatConfig(config *sys.HeartbeatConfig) error {
	// 检查是否已存在相同客户端的配置
	var existingConfig sys.HeartbeatConfig
	// 🚀 使用数据库连接池查询现有活跃配置
	err := dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_active_query", func(db *gorm.DB) error {
		return db.Where("client_id = ? AND is_active = ?", config.ClientID, true).First(&existingConfig).Error
	})
	if err == nil {
		return errors.New("该客户端已存在活跃的心跳配置")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// 设置默认值
	if config.IsActive == nil {
		config.IsActive = &[]bool{true}[0]
	}

	// 🚀 使用数据库连接池创建心跳配置
	return dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_create", func(db *gorm.DB) error {
		return db.Create(config).Error
	})
}

// DeleteHeartbeatConfig 删除心跳配置
func (h *HeartbeatConfigService) DeleteHeartbeatConfig(id uint) error {
	// 检查配置是否存在
	var config sys.HeartbeatConfig
	// 🚀 使用数据库连接池查询配置
	err := dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_delete_query", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&config).Error
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("配置不存在")
		}
		return err
	}

	// 不允许删除全局配置
	if config.ClientID == 0 {
		return errors.New("不允许删除全局配置")
	}

	// 🚀 使用数据库连接池删除心跳配置
	return dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_delete", func(db *gorm.DB) error {
		return db.Delete(&config).Error
	})
}

// GetHeartbeatConfigList 获取心跳配置列表
func (h *HeartbeatConfigService) GetHeartbeatConfigList(info request.PageInfo) ([]sys.HeartbeatConfig, int64, error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	var configs []sys.HeartbeatConfig
	var total int64

	// 🚀 使用数据库连接池进行分页查询
	err := dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_list", func(db *gorm.DB) error {
		query := db.Model(&sys.HeartbeatConfig{})

		// 计算总数
		if err := query.Count(&total).Error; err != nil {
			return err
		}

		// 获取分页数据
		return query.Limit(limit).Offset(offset).Order("client_id ASC, created_at DESC").Find(&configs).Error
	})

	if err != nil {
		return nil, 0, err
	}

	return configs, total, nil
}

// GetHeartbeatConfigForClient 为特定客户端获取心跳配置（用于心跳响应）
func (h *HeartbeatConfigService) GetHeartbeatConfigForClient(clientID uint) (*sys.HeartbeatConfig, error) {
	return sys.GetClientConfig(clientID)
}

// InitDefaultGlobalConfig 初始化默认全局配置
func (h *HeartbeatConfigService) InitDefaultGlobalConfig() error {
	global.LOG.Info("开始初始化默认全局心跳配置...")

	// 🚀 检查是否已存在全局配置
	var config sys.HeartbeatConfig
	err := dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_check", func(db *gorm.DB) error {
		return db.Where("client_id = ? AND is_active = ?", 0, true).First(&config).Error
	})
	if err == nil {
		// 已存在，不需要初始化
		global.LOG.Info("全局心跳配置已存在，跳过初始化",
			zap.Uint("id", config.ID),
			zap.Int("interval", config.Interval))
		return nil
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		global.LOG.Error("查询全局心跳配置时发生错误", zap.Error(err))
		return err
	}

	global.LOG.Info("未找到全局心跳配置，开始创建默认配置...")

	// 创建默认全局配置
	defaultConfig := &sys.HeartbeatConfig{
		ClientID:    0,
		Interval:    30,
		Timeout:     10,
		MaxRetries:  5,
		JitterRange: 5000,
		IsActive:    &[]bool{true}[0],
		Remark:      "系统默认全局心跳配置",
	}

	// 🚀 使用数据库连接池创建默认配置
	err = dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_create", func(db *gorm.DB) error {
		return db.Create(defaultConfig).Error
	})
	if err != nil {
		global.LOG.Error("创建默认全局心跳配置失败", zap.Error(err))
		return err
	}

	global.LOG.Info("成功创建默认全局心跳配置",
		zap.Uint("id", defaultConfig.ID),
		zap.Int("interval", defaultConfig.Interval))
	return nil
}
