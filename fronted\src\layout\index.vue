<template>
  <a-layout style="min-height: 100vh">
    <AppSidebar 
      @menu-select="handleMenuSelect" 
      :selectedKeys="selectedKeys" 
      :collapsed="collapsed"
    />
    <a-layout>
      <AppHeader 
        :selectedKeys="selectedKeys" 
        :collapsed="collapsed" 
        @update:collapsed="collapsed = $event"
        :userInfo="userInfo"
        @update:userInfo="updateUserInfo"
      />
      <a-layout-content class="content">
        <WorkspaceManager ref="workspaceManagerRef" />
      </a-layout-content>
      <AppFooter />
    </a-layout>

    <!-- 通知弹窗组件 -->
    <NotificationPopup
      ref="notificationPopupRef"
      @notification-click="handleNotificationClick"
      @notification-close="handleNotificationClose"
    />
  </a-layout>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import AppSidebar from '@/components/layout/AppSidebar.vue';
import AppHeader from '@/components/layout/AppHeader.vue';
import AppFooter from '@/components/layout/AppFooter.vue';
import WorkspaceManager from '@/components/layout/WorkspaceManager.vue';
import NotificationPopup from '@/components/notification/NotificationPopup.vue';
import notificationManager from '@/utils/notificationManager';

// 获取路由实例
const route = useRoute();

// 状态变量
const selectedKeys = ref(['1']);
const collapsed = ref(false);
const workspaceManagerRef = ref(null);
const notificationPopupRef = ref(null);
const userInfo = ref({
  username: 'Admin',
  avatar: '',
  role: 'superadmin',
  email: '<EMAIL>'
});

// 从本地存储加载用户信息
onMounted(() => {
  console.log('🚀 主布局组件挂载，检查用户登录状态...')

  const storedUserInfo = localStorage.getItem('userInfo');
  if (storedUserInfo) {
    try {
      userInfo.value = JSON.parse(storedUserInfo);
      console.log('✅ 用户信息已加载:', userInfo.value.username)
    } catch (error) {
      console.error('解析用户信息失败:', error);
    }
  }

  // 注册通知弹窗组件到通知管理器
  if (notificationPopupRef.value) {
    notificationManager.registerPopup(notificationPopupRef.value);
    console.log('📱 通知弹窗组件已注册到通知管理器')
  }

  // 🚨 关键修复：确保通知系统在页面刷新后也能启动
  const token = localStorage.getItem('token')
  if (token && userInfo.value.username) {
    console.log('🔔 检测到用户已登录，启动通知系统...')
    notificationManager.start()
  } else {
    console.log('⚠️ 用户未登录，跳过通知系统启动')
  }
});

// 组件卸载时清理
onUnmounted(() => {
  notificationManager.destroy();
});

// 更新用户信息
const updateUserInfo = (newUserInfo) => {
  userInfo.value = newUserInfo;
  localStorage.setItem('userInfo', JSON.stringify(newUserInfo));
};

// 处理菜单选择
const handleMenuSelect = (keys) => {
  selectedKeys.value = keys;
};

// 处理通知点击
const handleNotificationClick = (notification) => {
  console.log('通知被点击:', notification);

  // 根据通知类型跳转到相应页面
  if (notification.type && notification.type.startsWith('client_')) {
    // 客户端相关通知，跳转到客户端页面
    if (workspaceManagerRef.value) {
      workspaceManagerRef.value.handleRouteChange('/client');
    }
  } else if (notification.type && notification.type.startsWith('listener_')) {
    // 监听器相关通知，跳转到监听器页面
    if (workspaceManagerRef.value) {
      workspaceManagerRef.value.handleRouteChange('/listener');
    }
  }
};

// 处理通知关闭
const handleNotificationClose = (notification) => {
  console.log('通知被关闭:', notification);
};

// 监听路由变化
watch(() => route.path, (newPath) => {
  if (newPath.includes('/dashboard')) {
    selectedKeys.value = ['1'];
  } else if (newPath.includes('/listener')) {
    selectedKeys.value = ['2'];
  } else if (newPath.includes('/client')) {
    selectedKeys.value = ['3'];
  } else if (newPath === '/proxy/index') {
    selectedKeys.value = ['proxy-index'];
  } else if (newPath === '/proxy/chain') {
    selectedKeys.value = ['proxy-chain'];
  } else if (newPath === '/proxy/monitor') {
    selectedKeys.value = ['proxy-monitor'];
  } else if (newPath.includes('/download')) {
    selectedKeys.value = ['4'];
  } else if (newPath.includes('/screenshot')) {
    selectedKeys.value = ['5'];
  } else if (newPath.includes('/user-manage')) {
    selectedKeys.value = ['6'];
  } else if (newPath.includes('/log')) {
    selectedKeys.value = ['7'];
  } else if (newPath.includes('/performance')) {
    selectedKeys.value = ['8'];
  } else if (newPath.includes('/settings')) {
    selectedKeys.value = ['9'];
  }
  
  // 通知WorkspaceManager处理路由变化
  if (workspaceManagerRef.value) {
    workspaceManagerRef.value.handleRouteChange(newPath);
  }
}, { immediate: true });

defineExpose({
  selectedKeys,
  collapsed,
  userInfo
});
</script>

<style scoped>
.content {
  margin: 0;
  padding: 0;
  background: #f0f2f5;
  min-height: 280px;
  overflow: hidden;
}
</style>