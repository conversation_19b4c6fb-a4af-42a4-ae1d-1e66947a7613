package c2

import (
	"errors"
	"fmt"
	"server/core/dbpool"
	"server/core/manager"
	"server/factory"
	"server/global"
	"server/model/basic"
	"server/model/request/proxy"
	proxyResp "server/model/response/proxy"
	"server/model/sys"
	"server/model/task"
	"server/model/tlv"
	"server/utils"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ProxyService struct{}

func (s *ProxyService) CheckClientPortAvailability(clientID uint, req proxy.CheckPortRequest) (uint64, error) {
	proxyTask := &task.ProxyTask{
		ClientID: clientID,
		TaskType: "check_port",
		Status:   "pending",
	}
	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_check_port_task_create", func(db *gorm.DB) error {
		return db.Create(proxyTask).Error
	}); err != nil {
		return 0, err
	}
	go s.executeProxyTask(proxyTask, req)
	return proxyTask.ID, nil
}

func (s *ProxyService) CreateProxy(req proxy.ProxyCreateRequest) (proxy *basic.Proxy, err error) {
	var proxyInstance *basic.Proxy
	// 🚀 使用数据库连接池进行事务操作，确保代理创建的原子性
	err = dbpool.ExecuteDBTransaction("proxy_create", func(db *gorm.DB) error {
		if err := db.Model(&basic.Proxy{}).Where("name = ?", req.Name).First(proxyInstance).Error; err == nil {
			return errors.New("同名代理已存在")
		}

		proxy = &basic.Proxy{
			ProxyID:        fmt.Sprintf("proxy_%d_%d_%v", req.ClientID, time.Now().UnixNano(), req.Name),
			Name:           req.Name,
			Description:    req.Description,
			Type:           req.Type,
			UserPort:       req.UserPort,
			AllocMode:      req.AllocMode,
			AuthRequired:   req.AuthRequired,
			Username:       req.Username,
			Password:       req.Password,
			MaxConnections: req.MaxConnections,
			Timeout:        req.Timeout,
			ClientID:       req.ClientID,
			ListenerID:     req.ListenerID,
			Status:         0, // 初始状态为停止
			AllowedIPs:     req.AllowedIPs,
			BlockedIPs:     req.BlockedIPs,

			// 高级配置参数
			BufferSize:           req.BufferSize,
			RateLimit:            req.RateLimit,
			BandwidthLimit:       req.BandwidthLimit,
			ConnectionTimeout:    req.ConnectionTimeout,
			RetryCount:           req.RetryCount,
			RetryInterval:        req.RetryInterval,
			LoadBalanceStrategy:  req.LoadBalanceStrategy,
			HealthCheckInterval:  req.HealthCheckInterval,
			LogLevel:             req.LogLevel,
			MetricsCollection:    req.MetricsCollection,
		}

		if err := db.Create(proxy).Error; err != nil {
			global.LOG.Error("创建代理实例失败", zap.Error(err))
			return errors.New("创建代理实例失败")
		}

		return nil
	})

	if err != nil {
		return proxy, err
	}

	manager.GetGlobalProxyManager().AddProxy(proxy)
	global.LOG.Info("创建代理实例成功", zap.String("proxy_id", proxy.ProxyID), zap.String("name", proxy.Name))
	return proxy, nil
}

func (s *ProxyService) ControlProxy(req proxy.ProxyControlRequest) (uint64, error) {
	proxyInstance, exist := manager.GetGlobalProxyManager().GetProxy(req.ProxyID)
	if !exist {
		return 0, errors.New("代理实例不存在")
	}
	switch req.Action {
	case "start":
		global.LOG.Info("🚀 [SERVICE] 开始启动代理",
			zap.String("proxyID", req.ProxyID),
			zap.Uint("clientID", proxyInstance.ClientID),
			zap.String("type", proxyInstance.Type),
			zap.Uint16("userPort", proxyInstance.UserPort),
			zap.Int("currentStatus", proxyInstance.Status))

		if proxyInstance.Status == 1 {
			global.LOG.Warn("⚠️ [SERVICE] 代理实例已启动", zap.String("proxyID", req.ProxyID))
			return 0, errors.New("代理实例已启动")
		}

		startReq := proxy.ProxyStartRequest{
			ProxyID:     proxyInstance.ProxyID,
			Type:        proxyInstance.Type,
			AllocMode:   proxyInstance.AllocMode,
			Name:        proxyInstance.Name,
			UserPort:    proxyInstance.UserPort,
			ManualAlloc: proxyInstance.UserPort != 0,
		}

		global.LOG.Info("📤 [SERVICE] 发送启动请求到客户端",
			zap.String("proxyID", startReq.ProxyID),
			zap.String("type", startReq.Type),
			zap.String("allocMode", startReq.AllocMode),
			zap.Bool("manualAlloc", startReq.ManualAlloc),
			zap.Uint16("userPort", startReq.UserPort))

		return s.StartProxy(proxyInstance.ClientID, startReq)
	case "stop":
		if proxyInstance.Status == 0 {
			return 0, errors.New("代理实例已停止")
		}
		return s.StopProxy(proxyInstance.ClientID, proxy.ProxyStopRequest{
			ProxyID: proxyInstance.ProxyID,
		})
	case "restart":
		if proxyInstance.Status == 1 {
			s.StopProxy(proxyInstance.ClientID, proxy.ProxyStopRequest{
				ProxyID: proxyInstance.ProxyID,
			})
		}
		return s.StartProxy(proxyInstance.ClientID, proxy.ProxyStartRequest{
			ProxyID:     proxyInstance.ProxyID,
			Type:        proxyInstance.Type,
			AllocMode:   proxyInstance.AllocMode,
			Name:        proxyInstance.Name,
			UserPort:    proxyInstance.UserPort,
			ManualAlloc: proxyInstance.UserPort != 0,
		})

	default:
		return 0, errors.New("不支持的操作")
	}
}

// UpdateProxy 更新代理实例
func (s *ProxyService) UpdateProxy(req proxy.ProxyUpdateRequest) (proxyInstance *basic.Proxy, err error) {
	// 🚀 查找代理实例
	if err = dbpool.ExecuteDBOperationAsyncAndWait("proxy_update_query", func(db *gorm.DB) error {
		return db.Where("id = ?", req.ID).First(&proxyInstance).Error
	}); err != nil {
		return proxyInstance, errors.New("代理实例不存在")
	}

	// 检查代理是否正在运行
	if proxyInstance.Status == 1 {
		return proxyInstance, errors.New("代理正在运行，无法修改配置")
	}

	// 更新字段
	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	updates["auth_required"] = req.AuthRequired
	if req.Username != "" {
		updates["username"] = req.Username
	}
	if req.Password != "" {
		updates["password"] = req.Password
	}
	if req.MaxConnections > 0 {
		updates["max_connections"] = req.MaxConnections
	}
	if req.Timeout > 0 {
		updates["timeout"] = req.Timeout
	}
	if req.AllowedIPs != "" {
		updates["allowed_ips"] = req.AllowedIPs
	}
	if req.BlockedIPs != "" {
		updates["blocked_ips"] = req.BlockedIPs
	}

	// 更新高级配置参数
	if req.BufferSize > 0 {
		updates["buffer_size"] = req.BufferSize
	}
	if req.RateLimit >= 0 {
		updates["rate_limit"] = req.RateLimit
	}
	if req.BandwidthLimit >= 0 {
		updates["bandwidth_limit"] = req.BandwidthLimit
	}
	if req.ConnectionTimeout > 0 {
		updates["connection_timeout"] = req.ConnectionTimeout
	}
	if req.RetryCount >= 0 {
		updates["retry_count"] = req.RetryCount
	}
	if req.RetryInterval > 0 {
		updates["retry_interval"] = req.RetryInterval
	}
	if req.LoadBalanceStrategy != "" {
		updates["load_balance_strategy"] = req.LoadBalanceStrategy
	}
	if req.HealthCheckInterval > 0 {
		updates["health_check_interval"] = req.HealthCheckInterval
	}
	if req.LogLevel != "" {
		updates["log_level"] = req.LogLevel
	}
	updates["metrics_collection"] = req.MetricsCollection

	// 应用更新到内存对象
	if req.Name != "" {
		proxyInstance.Name = req.Name
	}
	if req.Description != "" {
		proxyInstance.Description = req.Description
	}
	proxyInstance.AuthRequired = req.AuthRequired
	if req.Username != "" {
		proxyInstance.Username = req.Username
	}
	if req.Password != "" {
		proxyInstance.Password = req.Password
	}
	if req.MaxConnections > 0 {
		proxyInstance.MaxConnections = req.MaxConnections
	}
	if req.Timeout > 0 {
		proxyInstance.Timeout = req.Timeout
	}
	if req.AllowedIPs != "" {
		proxyInstance.AllowedIPs = req.AllowedIPs
	}
	if req.BlockedIPs != "" {
		proxyInstance.BlockedIPs = req.BlockedIPs
	}

	// 更新高级配置参数到内存对象
	if req.BufferSize > 0 {
		proxyInstance.BufferSize = req.BufferSize
	}
	if req.RateLimit >= 0 {
		proxyInstance.RateLimit = req.RateLimit
	}
	if req.BandwidthLimit >= 0 {
		proxyInstance.BandwidthLimit = req.BandwidthLimit
	}
	if req.ConnectionTimeout > 0 {
		proxyInstance.ConnectionTimeout = req.ConnectionTimeout
	}
	if req.RetryCount >= 0 {
		proxyInstance.RetryCount = req.RetryCount
	}
	if req.RetryInterval > 0 {
		proxyInstance.RetryInterval = req.RetryInterval
	}
	if req.LoadBalanceStrategy != "" {
		proxyInstance.LoadBalanceStrategy = req.LoadBalanceStrategy
	}
	if req.HealthCheckInterval > 0 {
		proxyInstance.HealthCheckInterval = req.HealthCheckInterval
	}
	if req.LogLevel != "" {
		proxyInstance.LogLevel = req.LogLevel
	}
	proxyInstance.MetricsCollection = req.MetricsCollection

	// 通过ProxyManager更新（自动同步内存和数据库）
	err = manager.GetGlobalProxyManager().UpdateProxy(proxyInstance)
	if err != nil {
		global.LOG.Error("更新代理实例失败", zap.Error(err))
		return proxyInstance, errors.New("更新代理实例失败")
	}
	global.LOG.Info("更新代理实例成功", zap.String("proxy_id", proxyInstance.ProxyID))
	//TODO: 通知Client要重新加载配置
	s.StopProxy(proxyInstance.ClientID, proxy.ProxyStopRequest{
		ProxyID: proxyInstance.ProxyID,
	})

	s.StartProxy(proxyInstance.ClientID, proxy.ProxyStartRequest{
		ProxyID:     proxyInstance.ProxyID,
		Type:        proxyInstance.Type,
		AllocMode:   proxyInstance.AllocMode,
		Name:        proxyInstance.Name,
		UserPort:    proxyInstance.UserPort,
		ManualAlloc: proxyInstance.UserPort != 0,
	})
	return proxyInstance, nil
}

// GetProxyInstance 获取代理实例详情
func (s *ProxyService) GetProxyInstance(id uint) (proxyResponse proxyResp.ProxyInstanceResponse, err error) {
	var proxyInstance basic.Proxy
	var client *sys.Client

	// 🚀 使用数据库连接池进行查询操作
	err = dbpool.ExecuteDBOperationAsyncAndWait("proxy_instance_get", func(db *gorm.DB) error {
		if err := db.Where("id = ?", id).First(&proxyInstance).Error; err != nil {
			return errors.New("代理实例不存在")
		}

		if err := db.Where("id = ?", proxyInstance.ClientID).First(&client).Error; err != nil {
			return errors.New("客户端不存在")
		}

		return nil
	})

	if err != nil {
		return proxyResponse, err
	}
	// 填充客户端信息
	if proxyResponse.ClientInfo != nil {
		proxyResponse.ClientInfo = client
	}
	return proxyResponse, nil
}

func (p *ProxyService) StartProxy(clientID uint, req proxy.ProxyStartRequest) (uint64, error) {
	global.LOG.Info("🚀 [SERVICE] StartProxy开始执行",
		zap.Uint("clientID", clientID),
		zap.String("proxyID", req.ProxyID),
		zap.String("type", req.Type))

	if req.ProxyID == "" {
		global.LOG.Error("🔴 [SERVICE] 代理ID为空")
		return 0, errors.New("代理ID不能为空")
	}

	proxyInstance, exist := manager.GetGlobalProxyManager().GetProxy(req.ProxyID)
	if !exist {
		global.LOG.Error("🔴 [SERVICE] 代理实例不存在", zap.String("proxyID", req.ProxyID))
		return 0, errors.New("代理实例不存在")
	}

	global.LOG.Info("📋 [SERVICE] 找到代理实例",
		zap.String("proxyID", req.ProxyID),
		zap.String("name", proxyInstance.Name),
		zap.String("type", proxyInstance.Type),
		zap.Int("status", proxyInstance.Status),
		zap.Uint16("userPort", proxyInstance.UserPort))

	if proxyInstance.Status == 1 {
		global.LOG.Warn("⚠️ [SERVICE] 代理实例已启动", zap.String("proxyID", req.ProxyID))
		return 0, errors.New("代理实例已启动")
	}

	if err := manager.GetGlobalProxyManager().ValidateProxy(proxyInstance); err != nil {
		global.LOG.Error("🔴 [SERVICE] 代理实例配置验证失败",
			zap.String("proxyID", req.ProxyID),
			zap.Error(err))
		return 0, errors.New("代理实例配置不合法")
	}

	global.LOG.Info("✅ [SERVICE] 代理实例验证通过，创建任务")

	proxyTask := &task.ProxyTask{
		ClientID: clientID,
		ProxyID:  req.ProxyID,
		TaskType: "start_proxy",
		Status:   "pending",
	}
	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_start_task_create", func(db *gorm.DB) error {
		return db.Create(proxyTask).Error
	}); err != nil {
		return 0, err
	}
	go p.executeProxyTask(proxyTask, req)
	return proxyTask.ID, nil
}

func (p *ProxyService) StopProxy(clientID uint, req proxy.ProxyStopRequest) (uint64, error) {
	if req.ProxyID == "" {
		return 0, errors.New("代理ID不能为空")
	}
	var stopProxy *basic.Proxy
	// 🚀 使用数据库连接池进行查询操作
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_stop_query", func(db *gorm.DB) error {
		return db.Model(&basic.Proxy{}).Where("proxy_id = ?", req.ProxyID).Find(&stopProxy).Error
	}); err != nil {
		global.LOG.Error(fmt.Sprintf("找不到 Proxy ID=%s: ", req.ProxyID), zap.Error(err))
		return 0, err
	}
	// 停止代理时不检查端口可用性（因为端口正在被当前代理使用）
	if err := manager.GetGlobalProxyManager().ValidateProxy(stopProxy, false); err != nil {
		return 0, errors.New("代理实例配置不合法")
	}
	if stopProxy.Status == 0 {
		return 0, errors.New("代理实例已停止")
	}
	proxyTask := &task.ProxyTask{
		ClientID: clientID,
		ProxyID:  stopProxy.ProxyID,
		TaskType: "stop_proxy",
		Status:   "pending",
	}
	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_stop_task_create", func(db *gorm.DB) error {
		return db.Create(proxyTask).Error
	}); err != nil {
		return 0, err
	}
	go p.executeProxyTask(proxyTask, req)
	return proxyTask.ID, nil
}

func (p *ProxyService) DeleteProxy(id uint) (uint64, error) {
	var proxyInstance basic.Proxy
	// 🚀 使用数据库连接池进行查询操作
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_delete_query", func(db *gorm.DB) error {
		return db.Where("id = ?", id).First(&proxyInstance).Error
	}); err != nil {
		return 0, errors.New("代理实例不存在")
	}
	req := proxy.ProxyDeleteRequest{
		ProxyID:  proxyInstance.ProxyID,
		ClientID: proxyInstance.ClientID,
	}

	proxyTask := &task.ProxyTask{
		ProxyID:  proxyInstance.ProxyID,
		ClientID: proxyInstance.ClientID,
		TaskType: "delete_proxy",
		Status:   "pending",
	}
	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_delete_task_create", func(db *gorm.DB) error {
		return db.Create(proxyTask).Error
	}); err != nil {
		return 0, err
	}

	go p.executeProxyTask(proxyTask, req)
	return proxyTask.ID, nil
}

func (p *ProxyService) ListProxy(req proxy.ProxyListRequest) (uint64, error) {
	proxyTask := &task.ProxyTask{
		TaskType: "list_proxy",
		Status:   "pending",
	}
	// 🚀 使用数据库连接池异步创建任务
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_list_task_create", func(db *gorm.DB) error {
		return db.Create(proxyTask).Error
	}); err != nil {
		return 0, err
	}
	go p.executeProxyTask(proxyTask, req)
	return proxyTask.ID, nil
}

// handleListProxyTask 处理代理列表查询任务（不需要客户端连接）
func (p *ProxyService) handleListProxyTask(task *task.ProxyTask, req interface{}) {
	r, ok := req.(proxy.ProxyListRequest)
	if !ok {
		p.UpdateTaskStatus(task.ID, "failed", "请求参数类型错误")
		return
	}

	var proxies []basic.Proxy
	var total int64

	// 🚀 使用数据库连接池进行查询操作
	err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_list_query", func(db *gorm.DB) error {
		query := db.Model(&basic.Proxy{})

		// 添加过滤条件
		if r.ProxyID != "" {
			query = query.Where("proxy_id LIKE ?", "%"+r.ProxyID+"%")
		}
		if r.Name != "" {
			query = query.Where("name LIKE ?", "%"+r.Name+"%")
		}
		if r.Type != "" {
			query = query.Where("type = ?", r.Type)
		}
		if r.Status != nil {
			query = query.Where("status = ?", *r.Status)
		}
		if r.ClientID != nil {
			query = query.Where("client_id = ?", *r.ClientID)
		}

		// 获取总数
		if err := query.Count(&total).Error; err != nil {
			return err
		}

		// 分页查询
		offset := (r.Page - 1) * r.PageSize
		return query.Offset(offset).Limit(r.PageSize).Find(&proxies).Error
	})

	if err != nil {
		p.UpdateTaskStatus(task.ID, "failed", err.Error())
		return
	}

	// 🔗 为每个代理添加客户端信息
	proxyListWithClientInfo := make([]map[string]interface{}, 0, len(proxies))
	for _, proxy := range proxies {
		proxyMap := map[string]interface{}{
			"id":                     proxy.ID,
			"proxy_id":               proxy.ProxyID,
			"client_id":              proxy.ClientID,
			"listener_id":            proxy.ListenerID,
			"name":                   proxy.Name,
			"description":            proxy.Description,
			"type":                   proxy.Type,
			"alloc_mode":             proxy.AllocMode,
			"user_port":              proxy.UserPort,
			"client_port":            proxy.ClientPort,
			"auth_required":          proxy.AuthRequired,
			"username":               proxy.Username,
			"password":               proxy.Password,
			"max_connections":        proxy.MaxConnections,
			"timeout":                proxy.Timeout,
			"allowed_ips":            proxy.AllowedIPs,
			"blocked_ips":            proxy.BlockedIPs,
			"buffer_size":            proxy.BufferSize,
			"rate_limit":             proxy.RateLimit,
			"bandwidth_limit":        proxy.BandwidthLimit,
			"connection_timeout":     proxy.ConnectionTimeout,
			"retry_count":            proxy.RetryCount,
			"retry_interval":         proxy.RetryInterval,
			"load_balance_strategy":  proxy.LoadBalanceStrategy,
			"health_check_interval":  proxy.HealthCheckInterval,
			"log_level":              proxy.LogLevel,
			"metrics_collection":     proxy.MetricsCollection,
			"status":                 proxy.Status,
			"started_at":             proxy.StartedAt,
			"stopped_at":             proxy.StoppedAt,
			"last_error":             proxy.LastError,
			"error_count":            proxy.ErrorCount,
			"total_connections":      proxy.TotalConnections,
			"active_connections":     proxy.ActiveConnections,
			"bytes_transferred":      proxy.BytesTransferred,
			"bytes_received":         proxy.BytesReceived,
			"created_at":             proxy.CreatedAt,
			"updated_at":             proxy.UpdatedAt,
		}

		// 🔍 查询关联的客户端信息
		if proxy.ClientID > 0 {
			var client sys.Client
			err := dbpool.ExecuteDBOperationAsyncAndWait("get_client_info", func(db *gorm.DB) error {
				return db.Where("id = ?", proxy.ClientID).First(&client).Error
			})

			if err == nil {
				proxyMap["client_info"] = map[string]interface{}{
					"id":       client.ID,
					"hostname": client.Hostname,
					"os":       client.OS,
					"arch":     client.Architecture,
					"ip":       client.RemoteAddr,
					"status":   client.Status,
				}
			}
		}

		proxyListWithClientInfo = append(proxyListWithClientInfo, proxyMap)
	}

	result := map[string]interface{}{
		"list":      proxyListWithClientInfo,
		"total":     total,
		"page":      r.Page,
		"page_size": r.PageSize,
	}

	manager.ResponseMgr.StoreResponse(task.ID, "list_proxy", result, "")
	p.UpdateTaskStatus(task.ID, "completed", "")
}

func (p *ProxyService) executeProxyTask(task *task.ProxyTask, req interface{}) {
	// 🚀 对于list_proxy任务，不需要客户端连接，直接处理数据库查询
	if task.TaskType == "list_proxy" {
		p.handleListProxyTask(task, req)
		return
	}

	client, err := p.getOnlineClient(task.ClientID)
	if err != nil {
		p.UpdateTaskStatus(task.ID, "failed", err.Error())
		return
	}

	// 🚀 设置TaskID到请求中
	req = setProxyTaskIDToRequest(req, task.ID)

	switch r := req.(type) {
	// case proxy.CheckPortRequest:
	case proxy.ProxyStartRequest:
		global.LOG.Info("🔄 [SERVICE] 处理代理启动请求",
			zap.String("proxyID", task.ProxyID),
			zap.Uint64("taskID", task.ID))

		//从内存中获取，由于前面StartProxy已经保存过了，所以这里肯定是没问题的，不需要IO操作从数据库中读取，加快响应
		//由于内存存储的是指针，所以可以直接修改
		newProxy, exist := manager.GetGlobalProxyManager().GetProxy(task.ProxyID)
		if !exist {
			global.LOG.Error("🔴 [SERVICE] 代理不存在", zap.String("proxyID", task.ProxyID))
			p.UpdateTaskStatus(task.ID, "failed", "代理实例不存在")
			return
		}

		global.LOG.Info("📋 [SERVICE] 获取到代理实例",
			zap.String("proxyID", newProxy.ProxyID),
			zap.String("name", newProxy.Name),
			zap.String("type", newProxy.Type),
			zap.Uint16("currentUserPort", newProxy.UserPort),
			zap.Uint16("currentClientPort", newProxy.ClientPort))
		var port uint16
		//如果不是手动分配端口（看前端的表单），那么就Server这边自动生成一个用户端口
		if !r.ManualAlloc {
			global.LOG.Info("🎯 [SERVICE] 自动分配用户端口",
				zap.String("allocMode", r.AllocMode))
			port, err = manager.GetGlobalPortManager().AllocUserPort(r.AllocMode)
			if err != nil {
				global.LOG.Error("🔴 [SERVICE] 用户端口分配失败",
					zap.String("allocMode", r.AllocMode),
					zap.Error(err))
				p.UpdateTaskStatus(task.ID, "failed", err.Error())
				return
			}
			//原本这里是0，重新赋值一个新的端口
			newProxy.UserPort = port
			global.LOG.Info("✅ [SERVICE] 用户端口分配成功",
				zap.Uint16("userPort", port))
		} else {
			global.LOG.Info("📌 [SERVICE] 使用手动指定的用户端口",
				zap.Uint16("userPort", newProxy.UserPort))
		}

		// 正向代理：客户端自己决定SOCKS5端口，服务端不需要预分配
		if r.Type == "forward" {
			global.LOG.Info("🔄 [SERVICE] 正向代理模式，客户端将自行分配SOCKS5端口")
			newProxy.ClientPort = 0 // 标记为待分配，客户端响应后会更新
		} else {
			// 反向代理：服务端需要分配SOCKS5端口
			global.LOG.Info("🎯 [SERVICE] 反向代理模式，分配服务端SOCKS5端口")
			newProxy.ClientPort, err = manager.GetGlobalPortManager().AllocSocks5Port("random")
			r.ClientPort = newProxy.ClientPort
			if err != nil {
				global.LOG.Error("🔴 [SERVICE] 服务端SOCKS5端口分配失败", zap.Error(err))
				p.UpdateTaskStatus(task.ID, "failed", err.Error())
				return
			}
		}

		global.LOG.Info("✅ [SERVICE] 端口分配完成",
			zap.Uint16("userPort", newProxy.UserPort),
			zap.Uint16("clientPort", newProxy.ClientPort))
		// 反向代理：Server 开启 Socks5，让client来连接
		// 正向代理：Server 分配两个端口，一个连接client，一个给用户连接，这里不用自己启动服务器，只需要等待 Client 回复后再启动中继
		if r.Type == "reverse" {
			global.LOG.Info("🔄 [SERVICE] 反向代理模式，Server启动SOCKS5服务")
			if err = manager.GetGlobalProxyManager().StartProxy(newProxy); err != nil {
				global.LOG.Error("🔴 [SERVICE] 反向代理启动失败",
					zap.String("proxyID", newProxy.ProxyID),
					zap.Error(err))
				p.UpdateTaskStatus(task.ID, "failed", err.Error())
				return
			}
			newProxy.Status = 1 // 直接标记为运行中（因为 Server 已启动）
			global.LOG.Info("✅ [SERVICE] 反向代理Server端启动成功")
		} else {
			global.LOG.Info("🔄 [SERVICE] 正向代理模式，等待Client启动SOCKS5服务")
		}

		// 通过ProxyManager添加（自动同步内存和数据库）
		global.LOG.Info("💾 [SERVICE] 保存代理实例到内存和数据库")
		if err = manager.GetGlobalProxyManager().AddProxy(newProxy); err != nil {
			global.LOG.Error("🔴 [SERVICE] 保存代理实例失败",
				zap.String("proxyID", newProxy.ProxyID),
				zap.Error(err))
			p.UpdateTaskStatus(task.ID, "failed", err.Error())
			return
		}

		global.LOG.Info("✅ [SERVICE] 代理实例保存成功，发送启动命令到客户端",
			zap.String("proxyID", newProxy.ProxyID),
			zap.String("type", r.Type),
			zap.Uint16("userPort", newProxy.UserPort),
			zap.Uint16("clientPort", newProxy.ClientPort))
		// 更新请求中的 ProxyID
		r.ProxyID = newProxy.ProxyID
		req = r
	case proxy.ProxyStopRequest:
		newProxy, exist := manager.GetGlobalProxyManager().GetProxy(task.ProxyID)
		if !exist {
			global.LOG.Info("代理不存在")
			p.UpdateTaskStatus(task.ID, "failed", "代理实例不存在")
			return
		}
		// 根据类型停止服务
		// 反向代理：Server 关闭 Socks5
		if err = manager.GetGlobalProxyManager().StopProxy(newProxy); err != nil {
			p.UpdateTaskStatus(task.ID, "failed", err.Error())
			return
		}

		// 更新代理状态
		newProxy.Status = 0
		if err = manager.GetGlobalProxyManager().UpdateProxy(newProxy); err != nil {
			p.UpdateTaskStatus(task.ID, "failed", err.Error())
			return
		}
	case proxy.ProxyDeleteRequest:
		newProxy, exist := manager.GetGlobalProxyManager().GetProxy(task.ProxyID)
		if !exist {
			global.LOG.Info("代理不存在")
			p.UpdateTaskStatus(task.ID, "failed", "代理实例不存在")
			return
		}
		// 根据类型停止服务
		if newProxy.Type == "reverse" {
			// 反向代理：Server 关闭 Socks5
			if err = manager.GetGlobalProxyManager().StopReverseProxy(newProxy); err != nil {
				p.UpdateTaskStatus(task.ID, "failed", err.Error())
				return
			}
		} else {
			// 正向代理：Server 关闭中继服务
			if err = manager.GetGlobalProxyManager().StopForwardProxy(newProxy); err != nil {
				p.UpdateTaskStatus(task.ID, "failed", err.Error())
				return
			}
		}

		// 删除代理（自动同步内存和数据库）
		if err = manager.GetGlobalProxyManager().RemoveProxy(newProxy.ProxyID); err != nil {
			p.UpdateTaskStatus(task.ID, "failed", err.Error())
			return
		}
	}
	reqBytes, err := utils.SerializerManager.Serialize(req)
	if err != nil {
		p.UpdateTaskStatus(task.ID, "failed", err.Error())
		return
	}
	packet := &tlv.Packet{
		Header: &tlv.Header{
			Type: tlv.Proxy,
		},
		PacketData: &tlv.PacketData{
			Data: reqBytes,
		},
	}
	switch task.TaskType {
	case "check_port":
		packet.Header.Code = tlv.CheckPort
	case "start_proxy":
		packet.Header.Code = tlv.ProxyStart
	case "stop_proxy":
		packet.Header.Code = tlv.ProxyStop
	case "delete_proxy":
		packet.Header.Code = tlv.ProxyDelete
	}
	err = p.sendPacket(client, packet)
	if err != nil {
		p.UpdateTaskStatus(task.ID, "failed", err.Error())
		return
	}
	p.UpdateTaskStatus(task.ID, "running", fmt.Sprintf("%s请求已发送", task.TaskType))
}

// getOnlineClient 获取在线客户端
func (p *ProxyService) getOnlineClient(clientID uint) (*sys.Client, error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_client_online_check", func(db *gorm.DB) error {
		return db.Where("id = ? AND status = ?", clientID, 1).First(&client).Error
	})

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("客户端不在线或不存在")
		}
		return nil, err
	}
	return &client, nil
}

// sendPacket 发送数据包到客户端
func (p *ProxyService) sendPacket(client *sys.Client, packet *tlv.Packet) error {
	// 获取客户端连接并发送数据包
	return factory.SendPacketFactory(*client, packet)
}

// UpdateTaskStatus 更新任务状态
func (p *ProxyService) UpdateTaskStatus(taskID uint64, status, errorMsg string) {
	utils.UpdateProxyTaskStatus(taskID, status, errorMsg)
}

// setProxyTaskIDToRequest 设置TaskID到代理请求中（通用辅助函数）
func setProxyTaskIDToRequest(req interface{}, taskID uint64) interface{} {
	switch r := req.(type) {
	case *proxy.CheckPortRequest:
		r.TaskID = taskID
		return r
	case *proxy.ProxyListRequest:
		r.TaskID = taskID
		return r
	case *proxy.ProxyStartRequest:
		r.TaskID = taskID
		return r
	case *proxy.ProxyStopRequest:
		r.TaskID = taskID
		return r
	case *proxy.ProxyDeleteRequest:
		r.TaskID = taskID
		return r
	case proxy.CheckPortRequest:
		r.TaskID = taskID
		return r
	case proxy.ProxyListRequest:
		r.TaskID = taskID
		return r
	case proxy.ProxyStartRequest:
		r.TaskID = taskID
		return r
	case proxy.ProxyStopRequest:
		r.TaskID = taskID
		return r
	case proxy.ProxyDeleteRequest:
		r.TaskID = taskID
		return r
	default:
		global.LOG.Error("未知的代理请求类型", zap.Any("req", req))
		return req
	}
}
