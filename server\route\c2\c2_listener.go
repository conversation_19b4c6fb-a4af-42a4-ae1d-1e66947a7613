package c2

import (
	"server/middleware"

	"github.com/gin-gonic/gin"
)

type ListenerRoute struct{}

func (r *ListenerRoute) InitListenerRoute(Router *gin.RouterGroup) (IR gin.IRoutes) {
	listenerRouter := Router.Group("/listener").Use(middleware.OperationRecord())
	{
		listenerRouter.POST("", listenerApi.CreateListener)             // 创建监听器
		listenerRouter.DELETE("/:id", listenerApi.DeleteListener)       // 删除监听器
		listenerRouter.PUT("", listenerApi.UpdateListener)              // 更新监听器
		listenerRouter.GET("/:id", listenerApi.GetListener)             // 获取单个监听器
		listenerRouter.POST("/list", listenerApi.GetListenerList)       // 获取监听器列表
		listenerRouter.PUT("/status", listenerApi.UpdateListenerStatus) // 更新监听器状态
	}
	return listenerRouter
}
