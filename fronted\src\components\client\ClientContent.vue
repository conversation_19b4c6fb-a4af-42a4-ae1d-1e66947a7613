<template>
  <div class="client-content">
    <!-- 搜索和操作栏 -->
    <div class="action-bar">
      <SearchForm 
        :searchForm="searchForm" 
        @search="handleSearch" 
        @reset="resetSearch" 
        @refresh="getClientList"
      />
    </div>
    <!-- 数据表格 -->
    <ClientTable 
      :clientList="clientList" 
      :loading="loading" 
      :pagination="pagination" 
      @tableChange="handleTableChange" 
 
      @openManagement="openManagement" 
      @editRemark="showEditRemarkModal" 
      @disconnect="handleDisconnect" 
      @delete="handleDelete" 
    />

    <!-- 编辑备注弹窗 -->
    <RemarkModal 
      v-model:visible="remarkModalVisible" 
      :client="currentClient" 
      :submitLoading="submitLoading" 
      @submit="handleRemarkSubmit" 
    />




  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { clientApi } from '@/api';

// 导入子组件
import SearchForm from './SearchForm.vue';
import ClientTable from './ClientTable.vue';
import RemarkModal from './RemarkModal.vue';



// 接收父组件传递的属性
const props = defineProps({
  active: {
    type: Boolean,
    default: true
  }
});

// 状态变量
const loading = ref(false);
const clientList = ref([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

const searchForm = reactive({
  listenerType: undefined,
  status: undefined,
  remoteAddr: '',
  remark: '',
});

const remarkModalVisible = ref(false);
const currentClient = ref({});
const submitLoading = ref(false);

// 获取客户端列表
const getClientList = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm,
      // 添加statusSet字段，表示是否设置了status过滤
      statusSet: searchForm.status !== undefined && searchForm.status !== null
    };

    const res = await clientApi.getClientList(params);
    clientList.value = res.data.list;
    console.log(clientList.value);
    pagination.total = res.data.total;
  } catch (error) {
    console.error('获取客户端列表失败:', error);
    message.error('获取客户端列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理表格变化（分页、排序等）
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  getClientList();
};

// 处理搜索
const handleSearch = () => {
  pagination.current = 1;
  getClientList();
};

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'status' || key === 'listenerType' ? undefined : '';
  });
  pagination.current = 1;
  getClientList();
};

// 显示编辑备注弹窗
const showEditRemarkModal = (record) => {
  currentClient.value = { ...record };
  remarkModalVisible.value = true;
};

// 处理备注提交
const handleRemarkSubmit = async (client) => {
  try {
    submitLoading.value = true;
    const response = await clientApi.updateClientRemark({
      id: client.id,
      remark: client.remark
    });
    if (response.code === 200) {
      message.success(response.msg||'更新备注成功');
    }
    remarkModalVisible.value = false;
    getClientList();
  } catch (error) {
    console.error('更新备注失败:', error);
    message.error('更新备注失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    submitLoading.value = false;
  }
};

// 处理删除
const handleDelete = async (id) => {
  try {
    loading.value = true;
    await clientApi.deleteClient(id);
    message.success('删除成功');
    getClientList();
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 处理断开连接
const handleDisconnect = async (record) => {
  try {
    loading.value = true;
    await clientApi.disconnectClient({ id: record.id });
    message.success('断开连接成功');
    getClientList();
  } catch (error) {
    console.error('断开连接失败:', error);
    message.error('断开连接失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};



// 打开管理页面
const openManagement = (record) => {
  // 通过事件总线或者路由跳转到工作区管理页面
  // 这里需要与WorkspaceManager组件通信
  const event = new CustomEvent('openWorkspaceTab', {
    detail: {
      type: 'client-management',
      id: `client-${record.id}`,
      title: `客户端管理 - ${record.hostname || record.id}`,
      clientId: record.id,
      client: record
    }
  });
  window.dispatchEvent(event);
};

// 监听active属性变化
watch(() => props.active, (newVal) => {
  if (newVal) {
    getClientList();
  }
}, { immediate: true });

// 组件挂载时获取数据
onMounted(() => {
  if (props.active) {
    getClientList();
  }
});
</script>

<style scoped>
.client-content {
  padding: 16px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
  }
}
</style>