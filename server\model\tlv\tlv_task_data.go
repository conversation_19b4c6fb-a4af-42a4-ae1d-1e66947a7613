package tlv

import (
	"encoding/json"
	"errors"
	"sync"
	"time"
	"unsafe"
)

var (
	packetDataPool = sync.Pool{
		New: func() interface{} {
			return &PacketData{}
		},
	}

	bufferPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 4096) // 预分配4KB缓冲区
		},
	}

	fragmentPool = sync.Pool{
		New: func() interface{} {
			return make([][]byte, 0, 16) // 预分配16个分片槽位
		},
	}
)

// 数据包常量定义
const (
	IVSize            = 16                    // 初始化向量大小
	ChecksumSize      = 32                    // HMAC-SHA256校验和大小
	MinPacketDataSize = IVSize + ChecksumSize // 最小数据包大小
)

// EncryptedData 加密数据包结构
type PacketData struct {
	IV       [IVSize]byte       // 16字节初始化向量
	Checksum [ChecksumSize]byte // 32字节HMAC-SHA256校验和
	Data     []byte             // 加密数据
}

func (p *PacketData) Marshal() []byte {
	// 从内存池获取缓冲区
	buf := bufferPool.Get().([]byte)
	defer bufferPool.Put(buf[:0]) // 归还时重置长度

	// 预分配足够的容量
	totalSize := IVSize + ChecksumSize + len(p.Data)
	if cap(buf) < totalSize {
		buf = make([]byte, 0, totalSize)
	}
	buf = buf[:0]

	// 🚀 优化：使用unsafe进行批量内存拷贝
	buf = append(buf, (*[IVSize]byte)(unsafe.Pointer(&p.IV[0]))[:]...)
	buf = append(buf, (*[ChecksumSize]byte)(unsafe.Pointer(&p.Checksum[0]))[:]...)
	buf = append(buf, p.Data...)

	// 创建返回副本
	result := make([]byte, len(buf))
	copy(result, buf)
	return result
}

// 🚀 BOOST优化：零拷贝反序列化
func (p *PacketData) Unmarshal(PacketDataBytes []byte) error {
	if len(PacketDataBytes) < MinPacketDataSize {
		return errors.New("PacketData is too short")
	}

	// 🚀 优化：使用unsafe进行批量内存拷贝
	copy((*[IVSize]byte)(unsafe.Pointer(&p.IV[0]))[:], PacketDataBytes[:IVSize])
	copy((*[ChecksumSize]byte)(unsafe.Pointer(&p.Checksum[0]))[:], PacketDataBytes[IVSize:MinPacketDataSize])

	// 🚀 优化：避免额外的内存分配
	dataLen := len(PacketDataBytes) - MinPacketDataSize
	if cap(p.Data) < dataLen {
		p.Data = make([]byte, dataLen)
	} else {
		p.Data = p.Data[:dataLen]
	}
	copy(p.Data, PacketDataBytes[MinPacketDataSize:])

	return nil
}

// ProcessInfo 进程信息
type ProcessInfo struct {
	PID        int    `json:"pid"`        // 进程ID
	PPID       int    `json:"ppid"`       // 父进程ID
	Name       string `json:"name"`       // 进程名称
	CmdLine    string `json:"cmdline"`    // 命令行参数
	Executable string `json:"executable"` // 可执行文件路径
}

// NetworkInfo 网络信息
type NetworkInfo struct {
	Interfaces []NetworkInterface `json:"interfaces"` // 网络接口列表
	PublicIP   string             `json:"public_ip"`  // 公网IP
	LocalIP    string             `json:"local_ip"`   // 本地IP
}

// NetworkInterface 网络接口
type NetworkInterface struct {
	Name      string   `json:"name"`      // 接口名称
	Addresses []string `json:"addresses"` // IP地址列表
	MAC       string   `json:"mac"`       // MAC地址
	MTU       int      `json:"mtu"`       // 最大传输单元
	Up        bool     `json:"up"`        // 是否启用
}

// SystemInfo 系统信息
type SystemInfo struct {
	KernelVersion string `json:"kernel_version"` // 内核版本
	Uptime        int64  `json:"uptime"`         // 系统运行时间(秒)
	BootTime      int64  `json:"boot_time"`      // 启动时间戳
	CPUCount      int    `json:"cpu_count"`      // CPU核心数
	MemoryTotal   uint64 `json:"memory_total"`   // 总内存(字节)
	MemoryFree    uint64 `json:"memory_free"`    // 可用内存(字节)
	DiskTotal     uint64 `json:"disk_total"`     // 总磁盘空间(字节)
	DiskFree      uint64 `json:"disk_free"`      // 可用磁盘空间(字节)
	// 🚀 新增：Linux发行版信息
	Distribution  string `json:"distribution"`   // Linux发行版名称
	DistroVersion string `json:"distro_version"` // 发行版版本
}

// SecurityInfo 安全信息
type SecurityInfo struct {
	Privileged bool     `json:"privileged"`  // 是否具有管理员权限
	UserGroups []string `json:"user_groups"` // 用户所属组
	AntiVirus  []string `json:"antivirus"`   // 检测到的杀毒软件
	Firewall   bool     `json:"firewall"`    // 防火墙状态
	SELinux    string   `json:"selinux"`     // SELinux状态(Linux)
	UAC        bool     `json:"uac"`         // UAC状态(Windows)
	SIP        bool     `json:"sip"`         // SIP状态(macOS)
}

// EnvironmentInfo 环境信息
type EnvironmentInfo struct {
	WorkingDir string            `json:"working_dir"` // 当前工作目录
	HomeDir    string            `json:"home_dir"`    // 用户主目录
	TempDir    string            `json:"temp_dir"`    // 临时目录
	Path       string            `json:"path"`        // PATH环境变量
	EnvVars    map[string]string `json:"env_vars"`    // 重要环境变量
	Timezone   string            `json:"timezone"`    // 时区
	Language   string            `json:"language"`    // 系统语言
}

// METADATA 客户端元数据
type METADATA struct {
	// 会话密钥(不再使用指针)
	EncryptionKey []byte `json:"encryption_key"` // 加密密钥
	HmacKey       []byte `json:"hmac_key"`       // HMAC密钥

	// 基本信息
	ShellType    string `json:"shell_type"`   // Shell类型
	Username     string `json:"username"`     // 用户名
	Hostname     string `json:"hostname"`     // 主机名
	OS           string `json:"os"`           // 操作系统
	Architecture string `json:"architecture"` // 系统架构

	// 详细信息
	Process     ProcessInfo     `json:"process"`     // 进程信息
	Network     NetworkInfo     `json:"network"`     // 网络信息
	System      SystemInfo      `json:"system"`      // 系统信息
	Security    SecurityInfo    `json:"security"`    // 安全信息
	Environment EnvironmentInfo `json:"environment"` // 环境信息

	// 时间戳
	ConnectTime int64  `json:"connect_time"` // 连接时间戳
	ClientID    string `json:"client_id"`    // 客户端唯一标识
}

// NewMetadata 创建新的元数据
func NewMetadata(encKey, hmacKey []byte, shellType, username, hostname, os, arch string) *METADATA {
	return &METADATA{
		EncryptionKey: encKey,
		HmacKey:       hmacKey,
		ShellType:     shellType,
		Username:      username,
		Hostname:      hostname,
		OS:            os,
		Architecture:  arch,
		ConnectTime:   time.Now().Unix(),
	}
}

// Marshal 序列化为JSON
func (m *METADATA) Marshal() ([]byte, error) {
	return json.Marshal(m)
}

// Unmarshal 从JSON反序列化
func (m *METADATA) Unmarshal(data []byte) error {
	return json.Unmarshal(data, m)
}

// CommandRequest 多终端命令请求结构体
type CommandRequest struct {
	TerminalID uint32 `json:"terminal_id"` // 终端ID: 0=主终端, 1000+=备用终端
	Command    string `json:"command"`     // 要执行的命令
	Type       uint8  `json:"type"`        // 命令类型（预留字段）
}

// CommandResponse 多终端命令响应结构体
type CommandResponse struct {
	TerminalID uint32 `json:"terminal_id"` // 对应的终端ID
	Output     string `json:"output"`      // 命令输出
	Success    bool   `json:"success"`     // 是否成功执行
	Error      string `json:"error"`       // 错误信息（如果有）
}

// Marshal 序列化CommandRequest为JSON
func (c *CommandRequest) Marshal() ([]byte, error) {
	return json.Marshal(c)
}

// Unmarshal 从JSON反序列化CommandRequest
func (c *CommandRequest) Unmarshal(data []byte) error {
	return json.Unmarshal(data, c)
}

// Marshal 序列化CommandResponse为JSON
func (c *CommandResponse) Marshal() ([]byte, error) {
	return json.Marshal(c)
}

// Unmarshal 从JSON反序列化CommandResponse
func (c *CommandResponse) Unmarshal(data []byte) error {
	return json.Unmarshal(data, c)
}
