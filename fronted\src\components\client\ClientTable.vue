<template>
  <div class="client-table">
    <a-table
      :columns="columns"
      :tableLayout="'auto'"
      :data-source="clientList"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      rowKey="id"
    >
      <!-- 操作系统列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'os'">
          <div class="os-cell">
            <SystemIcons
              :type="getOSIconType(record.os)"
              size="small"
              :status="record.status === 1 ? 'online' : 'offline'"
              class="os-icon"
            />
            <span class="os-text">{{ getOSDisplayName(record.os) }}</span>
          </div>
        </template>

        <!-- 状态列 -->
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="record.status === 1 ? 'green' : 'red'">
            {{ record.status === 1 ? '在线' : '离线' }}
          </a-tag>
        </template>

        <!-- 连接时间列 -->
        <template v-if="column.dataIndex === 'connectedAt'">
          {{ formatDate(record.connectedAt) }}
        </template>

        <!-- 最后活动时间列 -->
        <template v-if="column.dataIndex === 'lastActiveAt'">
          {{ formatDate(record.lastActiveAt) }}
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <a-space>

            <a-button
              type="default" 
              size="small" 
              @click="openManagement(record)"
            >
              管理
            </a-button>
            <a-button 
              type="link" 
              size="small" 
              @click="showEditRemarkModal(record)"
            >
              编辑备注
            </a-button>
            <a-button 
              type="link" 
              size="small" 
              :disabled="record.status !== 1"
              @click="handleDisconnect(record)"
              danger
            >
              断开连接
            </a-button>
            <a-popconfirm
              title="确定要删除这个客户端吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record.id)"
            >
              <a-button type="link" danger size="small">删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { formatDate } from '@/utils/format';
import SystemIcons from '@/components/icons/SystemIcons.vue';

// 定义属性
const props = defineProps({
  clientList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    required: true
  }
});

// 定义事件
const emit = defineEmits([
  'tableChange', 
  'openManagement',
  'editRemark', 
  'disconnect', 
  'delete'
]);

// 表格列定义
const columns = [
  {
    title: 'ID',
    align: 'center',
    dataIndex: 'id',
    width: 60,
  },
  {
    title: '监听器ID',
    align: 'center',
    dataIndex: 'listenerId',
    width: 80,
  },
  {
    title: '操作系统',
    align: 'center',
    dataIndex: 'os',
    width: 120,
  },
  {
    title: '用户名',
    align: 'center',
    dataIndex: 'username',
    width: 100,
  },
  {
    title: '主机名',
    align: 'center',
    dataIndex: 'hostname',
    width: 120,
  },
  {
    title: '进程名',
    align: 'center',
    dataIndex: 'processName',
    width: 120,
  },
  {
    title: '架构',
    align: 'center',
    dataIndex: 'architecture',
    width: 80,
  },
  {
    title: '远程地址',
    align: 'center',
    dataIndex: 'remoteAddr',
    width: 150,
  },
  {
    title: '内网地址',
    align: 'center',
    dataIndex: 'local_ip',
    width: 120,
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status',
    width: 80,
  },
  {
    title: '连接时间',
    align: 'center',
    dataIndex: 'connectedAt',
    width: 150,
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
  },
  {
    title: '操作',
    align: 'center',
    dataIndex: 'action',
    width: 280,
    fixed: 'right',
  },
];

// 处理表格变化（分页、排序等）
const handleTableChange = (pag) => {
  emit('tableChange', pag);
};



// 打开管理页面
const openManagement = (record) => {
  emit('openManagement', record);
};

// 显示编辑备注弹窗
const showEditRemarkModal = (record) => {
  emit('editRemark', record);
};

// 处理断开连接
const handleDisconnect = (record) => {
  emit('disconnect', record);
};

// 处理删除
const handleDelete = (id) => {
  emit('delete', id);
};

// 获取操作系统图标类型
const getOSIconType = (os) => {
  if (!os) return 'default';

  const osLower = os.toLowerCase();

  // Windows系统
  if (osLower.includes('windows')) {
    return 'windows';
  }

  // macOS/Darwin系统
  if (osLower.includes('darwin') || osLower.includes('macos') || osLower.includes('mac')) {
    return 'macos';
  }

  // Linux发行版
  if (osLower.includes('ubuntu')) {
    return 'ubuntu';
  }
  if (osLower.includes('debian')) {
    return 'debian';
  }
  if (osLower.includes('kali')) {
    return 'kali';
  }
  if (osLower.includes('centos')) {
    return 'centos';
  }
  if (osLower.includes('redhat') || osLower.includes('rhel')) {
    return 'redhat';
  }
  if (osLower.includes('fedora')) {
    return 'fedora';
  }
  if (osLower.includes('suse') || osLower.includes('opensuse')) {
    return 'suse';
  }
  if (osLower.includes('arch')) {
    return 'arch';
  }

  // 通用Linux/Unix
  if (osLower.includes('linux') || osLower.includes('unix')) {
    return 'linux';
  }

  // 移动端
  if (osLower.includes('android')) {
    return 'android';
  }
  if (osLower.includes('ios')) {
    return 'ios';
  }

  return 'default';
};

// 获取操作系统显示名称
const getOSDisplayName = (os) => {
  if (!os) return '未知';

  const osLower = os.toLowerCase();

  // Windows系统
  if (osLower.includes('windows')) {
    return 'Windows';
  }

  // macOS/Darwin系统
  if (osLower.includes('darwin') || osLower.includes('macos') || osLower.includes('mac')) {
    return 'macOS';
  }

  // Linux发行版
  if (osLower.includes('ubuntu')) {
    return 'Ubuntu';
  }
  if (osLower.includes('debian')) {
    return 'Debian';
  }
  if (osLower.includes('kali')) {
    return 'Kali Linux';
  }
  if (osLower.includes('centos')) {
    return 'CentOS';
  }
  if (osLower.includes('redhat') || osLower.includes('rhel')) {
    return 'Red Hat';
  }
  if (osLower.includes('fedora')) {
    return 'Fedora';
  }
  if (osLower.includes('suse') || osLower.includes('opensuse')) {
    return 'SUSE';
  }
  if (osLower.includes('arch')) {
    return 'Arch Linux';
  }

  // 通用Linux/Unix
  if (osLower.includes('linux')) {
    return 'Linux';
  }
  if (osLower.includes('unix')) {
    return 'Unix';
  }

  // 移动端
  if (osLower.includes('android')) {
    return 'Android';
  }
  if (osLower.includes('ios')) {
    return 'iOS';
  }

  // 返回原始值（首字母大写）
  return os.charAt(0).toUpperCase() + os.slice(1);
};
</script>

<style scoped>
.client-table {
  margin-bottom: 16px;
}

.os-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.os-icon {
  width: 20px;
  height: 20px;
}

.os-text {
  font-weight: 500;
  color: #262626;
}

.os-cell .anticon-question-circle {
  color: #8c8c8c;
}
</style>