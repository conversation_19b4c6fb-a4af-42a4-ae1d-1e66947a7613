package tlv

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"errors"
	"fmt"
	"io"
	"server/utils"
)

type Packet struct {
	Header     *Header
	PacketData *PacketData
}

const (
	MinPacketSize = MinPacketDataSize + HeaderSize
	PrefixSize    = 8
)

// Serialize 序列化Data数据包为字节流
func (p *Packet) Serialize() []byte {
	header := *p.Header
	packetData := *p.PacketData
	buf := new(bytes.Buffer)
	ran, err := utils.RandomString(8)
	if err != nil {
		ran = "aabbccdd"
	}
	buf.Write([]byte(ran))
	buf.Write(header.Marshal())
	buf.Write(packetData.Marshal())
	return buf.Bytes()
}

// DeserializePacket 从字节流解析数据包
func (p *Packet) DeserializePacket(serializedPacketWithPrefix []byte) error {
	serializedPacket := serializedPacketWithPrefix[8:]
	if len(serializedPacket) < MinPacketSize {
		return fmt.Errorf("packet too small: %d < %d", len(serializedPacket), MinPacketSize)
	}
	headerBytes := serializedPacket[:HeaderSize]
	dataBytes := serializedPacket[HeaderSize:]
	var header Header
	var packetData PacketData
	err := header.Unmarshal(headerBytes)
	if err != nil {
		return err
	}
	err = packetData.Unmarshal(dataBytes)
	if err != nil {
		return err
	}
	p.Header = &header
	p.PacketData = &packetData
	return nil
}

// 🚀 BOOST优化：使用AES-GCM替代AES-CTR+HMAC (一步完成加密+认证)
func (p *Packet) EncryptPacket(metadata *METADATA) error {
	// 1. 准备明文数据
	plaintext := p.PacketData.Data

	// 2. 生成随机IV (AES-GCM使用12字节nonce)
	nonce := make([]byte, 12)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return err
	}

	// 将nonce存储在IV的前12字节
	copy(p.PacketData.IV[:12], nonce)

	// 3. 🚀 使用AES-GCM进行认证加密
	block, err := aes.NewCipher(metadata.EncryptionKey[:32]) // AES需要32字节密钥
	if err != nil {
		return err
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return err
	}

	// 4. 🚀 一步完成加密+认证，比AES-CTR+HMAC更快更安全
	ciphertext := aead.Seal(nil, nonce, plaintext, nil)

	// 5. 分离密文和认证标签
	if len(ciphertext) < aead.Overhead() {
		return errors.New("ciphertext too short")
	}

	authTagLen := aead.Overhead() // GCM认证标签长度
	actualCiphertext := ciphertext[:len(ciphertext)-authTagLen]
	authTag := ciphertext[len(ciphertext)-authTagLen:]

	p.PacketData.Data = actualCiphertext
	copy(p.PacketData.Checksum[:authTagLen], authTag) // 存储认证标签

	// 6. 更新长度
	serializedSize := HeaderSize + MinPacketDataSize + len(actualCiphertext)
	p.Header.Length = uint32(serializedSize)

	return nil
}

// 🚀 BOOST优化：使用AES-GCM解密
func (p *Packet) DecryptPacket(metadata *METADATA) error {
	// 1. 提取nonce
	nonce := p.PacketData.IV[:12]

	// 2. 🚀 使用AES-GCM进行认证解密
	block, err := aes.NewCipher(metadata.EncryptionKey[:32])
	if err != nil {
		return err
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return err
	}

	// 3. 重构完整的密文（数据+认证标签）
	authTagLen := aead.Overhead()
	authTag := p.PacketData.Checksum[:authTagLen]
	fullCiphertext := make([]byte, len(p.PacketData.Data)+authTagLen)
	copy(fullCiphertext, p.PacketData.Data)
	copy(fullCiphertext[len(p.PacketData.Data):], authTag)

	// 4. 🚀 一步完成解密+验证，比AES-CTR+HMAC更快更安全
	plaintext, err := aead.Open(nil, nonce, fullCiphertext, nil)
	if err != nil {
		return errors.New("decryption or authentication failed")
	}

	// 5. 更新数据
	p.PacketData.Data = plaintext
	return nil
}

// FragmentData 将大数据分片
func FragmentData(data []byte, maxFragmentSize int) [][]byte {
	if len(data) <= maxFragmentSize {
		return [][]byte{data}
	}

	var fragments [][]byte
	for i := 0; i < len(data); i += maxFragmentSize {
		end := i + maxFragmentSize
		if end > len(data) {
			end = len(data)
		}
		fragments = append(fragments, data[i:end])
	}
	return fragments
}

// CreateFragmentedPackets 创建分片数据包
func CreateFragmentedPackets(taskType, taskCode uint8, label uint32, data []byte, maxFragmentSize int) []*Packet {
	fragments := FragmentData(data, maxFragmentSize)
	packets := make([]*Packet, len(fragments))

	for i, fragment := range fragments {
		header := &Header{
			Type:      taskType,
			Code:      taskCode,
			Label:     label,
			FragIndex: uint32(i),
		}

		// 设置分片标志
		if len(fragments) == 1 {
			header.Flags = Nop // 无分片
		} else if i == len(fragments)-1 {
			header.Flags = NoMoreFrag // 最后一个分片
		} else {
			header.Flags = MoreFrag // 还有更多分片
		}

		packetData := &PacketData{
			Data: fragment,
		}

		packets[i] = &Packet{
			Header:     header,
			PacketData: packetData,
		}
	}

	return packets
}

// ReassembleFragments 重组分片数据
type FragmentBuffer struct {
	Fragments map[uint32][]byte
	Expected  int
	Received  int
}

func NewFragmentBuffer() *FragmentBuffer {
	return &FragmentBuffer{
		Fragments: make(map[uint32][]byte),
	}
}

func (fb *FragmentBuffer) AddFragment(header *Header, data []byte) ([]byte, bool) {
	fb.Fragments[header.FragIndex] = data
	fb.Received++

	// 如果是最后一个分片，设置期望的总分片数
	if header.Flags == NoMoreFrag {
		fb.Expected = int(header.FragIndex) + 1
	}

	// 检查是否收集完所有分片
	if fb.Expected > 0 && fb.Received == fb.Expected {
		// 重组数据
		var result []byte
		for i := 0; i < fb.Expected; i++ {
			if fragment, exists := fb.Fragments[uint32(i)]; exists {
				result = append(result, fragment...)
			} else {
				return nil, false // 缺少分片
			}
		}
		return result, true
	}

	return nil, false
}
