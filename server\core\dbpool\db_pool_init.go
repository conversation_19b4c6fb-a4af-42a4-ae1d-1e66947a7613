package dbpool

import (
	"runtime"
	"server/global"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InitGlobalDBPoolManager 初始化全局数据库连接池管理器
func InitGlobalDBPoolManager(db *gorm.DB) error {
	// 根据CPU核心数和系统配置计算连接池参数
	cpuCount := runtime.NumCPU()
	
	config := DBPoolConfig{
		MaxOpenConns:        calculateMaxOpenConns(cpuCount),
		MaxIdleConns:        calculateMaxIdleConns(cpuCount),
		ConnMaxLifetime:     time.Hour,
		ConnMaxIdleTime:     10 * time.Minute,
		HealthCheckInterval: 30 * time.Second,
		HealthCheckTimeout:  5 * time.Second,
		QueryTimeout:        30 * time.Second,
		SlowQueryThreshold:  100 * time.Millisecond,
	}
	
	// 从配置文件读取自定义配置（如果存在）
	if global.CONFIG.Sqlite.MaxOpenConns > 0 {
		config.MaxOpenConns = global.CONFIG.Sqlite.MaxOpenConns
	}
	if global.CONFIG.Sqlite.MaxIdleConns > 0 {
		config.MaxIdleConns = global.CONFIG.Sqlite.MaxIdleConns
	}
	
	var err error
	GlobalDBPoolManager, err = NewDBPoolManager(db, config)
	if err != nil {
		return err
	}
	
	// 启动数据库连接池管理器
	GlobalDBPoolManager.Start()
	
	global.LOG.Info("全局数据库连接池管理器初始化完成",
		zap.Int("maxOpenConns", config.MaxOpenConns),
		zap.Int("maxIdleConns", config.MaxIdleConns),
		zap.Duration("healthCheckInterval", config.HealthCheckInterval))
	
	return nil
}

// calculateMaxOpenConns 计算最大打开连接数
func calculateMaxOpenConns(cpuCount int) int {
	// 基于CPU核心数计算，但不超过50个连接
	maxConns := cpuCount * 4
	if maxConns > 50 {
		maxConns = 50
	}
	if maxConns < 10 {
		maxConns = 10
	}
	return maxConns
}

// calculateMaxIdleConns 计算最大空闲连接数
func calculateMaxIdleConns(cpuCount int) int {
	// 空闲连接数通常是最大连接数的1/4到1/2
	idleConns := cpuCount
	if idleConns > 10 {
		idleConns = 10
	}
	if idleConns < 2 {
		idleConns = 2
	}
	return idleConns
}

// ExecuteWithMonitoring 全局数据库操作监控函数
func ExecuteWithMonitoring(operation string, fn func() error) error {
	if GlobalDBPoolManager == nil {
		global.LOG.Warn("数据库连接池管理器未初始化，直接执行操作")
		return fn()
	}
	return GlobalDBPoolManager.ExecuteWithMonitoring(operation, fn)
}

// ExecuteWithRetry 全局数据库操作重试函数
func ExecuteWithRetry(operation string, fn func() error, maxRetries int, retryDelay time.Duration) error {
	if GlobalDBPoolManager == nil {
		global.LOG.Warn("数据库连接池管理器未初始化，直接执行操作")
		return fn()
	}
	return GlobalDBPoolManager.ExecuteWithRetry(operation, fn, maxRetries, retryDelay)
}

// GetGlobalDBPoolStats 获取全局数据库连接池统计信息
func GetGlobalDBPoolStats() *DBPoolStats {
	if GlobalDBPoolManager == nil {
		return &DBPoolStats{}
	}
	return GlobalDBPoolManager.GetStats()
}

// GetGlobalDBPoolStatsCompatible 获取全局数据库连接池兼容格式统计信息
func GetGlobalDBPoolStatsCompatible() map[string]interface{} {
	if GlobalDBPoolManager == nil {
		return map[string]interface{}{
			"max_open_conns":        0,
			"max_idle_conns":        0,
			"open_conns":            0,
			"in_use_conns":          0,
			"idle_conns":            0,
			"total_queries":         int64(0),
			"success_queries":       int64(0),
			"failed_queries":        int64(0),
			"slow_queries":          int64(0),
			"health_status":         "未初始化",
		}
	}
	return GlobalDBPoolManager.GetCompatibleStats()
}

// StopGlobalDBPoolManager 停止全局数据库连接池管理器
func StopGlobalDBPoolManager() {
	if GlobalDBPoolManager != nil {
		GlobalDBPoolManager.Stop()
		global.LOG.Info("全局数据库连接池管理器已停止")
	}
}
