//go:build darwin
// +build darwin
package common

import (
	"context"
	"crypto/hmac"
	"crypto/rsa"
	"fmt"
	"log"
	"net"
	"os"
	"os/exec"
	"os/user"
	"runtime"
	"sync"
	"time"

	"github.com/creack/pty"
	"github.com/minio/sha256-simd"
)

// InterfaceSpeedCache 网络接口速度缓存
type InterfaceSpeedCache struct {
	LastBytesSent     uint64    // 上次发送字节数
	LastBytesReceived uint64    // 上次接收字节数
	LastTimestamp     time.Time // 上次采样时间
	CurrentUpSpeed    float64   // 当前上传速度 (B/s)
	CurrentDownSpeed  float64   // 当前下载速度 (B/s)
}

type ConnectionManager struct {
	conn       net.Conn
	mu         sync.Mutex
	ctx        context.Context
	cancel     context.CancelFunc
	retryCount int
	config     *ShellConfig

	cmdMutex      sync.Mutex
	cmd           *exec.Cmd
	ptmx          *os.File
	terminalSize  *TerminalSize
	lastCommand   string
	ptyOutput<PERSON>han chan []byte       // PTY 输出通道
	ptyInputChan  chan []byte       // PTY 输入通道
	resizeChan    chan *pty.Winsize // 终端大小调整通道

	serializer     Serializer
	fragmentBuffer map[uint32]*FragmentBuffer // 按Label分组的分片缓冲区
	fragMutex      sync.Mutex
	metadata       *METADATA
	labelCounter   uint64
	RsaPublicKey   *rsa.PublicKey

	// =====内存池优化=====
	memoryPool     *MemoryPool // 统一内存池管理
	HmacPool       sync.Pool   // HMAC计算池
	PacketPool     sync.Pool   // 数据包池

	// =====网络监控缓存=====
	cachedNetstatOutput string                          // 缓存netstat输出，避免重复执行
	interfaceSpeedCache map[string]*InterfaceSpeedCache // 网络接口速度缓存
	interfaceCacheMutex sync.RWMutex                    // 接口缓存互斥锁
}
type TerminalSize struct {
	size *pty.Winsize
}

type ShellConfig struct {
	ServerAddr string
}

func NewConnectionManager(config *ShellConfig, PublicKey *rsa.PublicKey) *ConnectionManager {
	ctx, cancel := context.WithCancel(context.Background())
	encKey, hmacKey := NewSessionKeys()
	log.Println(fmt.Sprintf("【Client】协商的会话密钥: AES[%x...] HMAC[%x...]",
		encKey[:8],
		hmacKey[:8]))
	username := "unknown"
	if u, err := user.Current(); err == nil {
		username = u.Username
	}
	hostname, err := os.Hostname()
	if err != nil {
		log.Printf("获取主机名失败: %v", err)
		hostname = "unknown"
	}
	osName := runtime.GOOS
	arch := runtime.GOARCH

	metadata := NewMetadata(
		encKey,
		hmacKey,
		"interactive",
		username,
		hostname,
		osName,
		arch,
	)

	cm := &ConnectionManager{
		config:   config,
		ctx:      ctx,
		cancel:   cancel,
		metadata: metadata,
		terminalSize: &TerminalSize{
			size: &pty.Winsize{Cols: 148, Rows: 50}, // 使用固定的终端大小
		},
		labelCounter:        0,
		fragmentBuffer:      make(map[uint32]*FragmentBuffer),
		RsaPublicKey:        PublicKey,
		serializer:          Serializer{},
		interfaceSpeedCache: make(map[string]*InterfaceSpeedCache),

		// 初始化内存池
		memoryPool: NewMemoryPool(),

		HmacPool: sync.Pool{
			New: func() interface{} {
				return hmac.New(sha256.New, metadata.HmacKey)
			},
		},

		PacketPool: sync.Pool{
			New: func() interface{} {
				return &Packet{
					Header:     &Header{},
					PacketData: &PacketData{},
				}
			},
		},
	}

	log.Println("🚀 macOS客户端内存池初始化完成")
	return cm
}
