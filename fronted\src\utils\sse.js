/**
 * SSE (Server-Sent Events) 工具类
 * 用于处理服务器推送的实时数据
 */

class SSEManager {
  constructor() {
    this.connections = new Map() // 存储所有SSE连接
  }

  /**
   * 创建SSE连接
   * @param {string} url SSE接口地址
   * @param {Object} options 配置选项
   * @returns {EventSource} SSE连接实例
   */
  connect(url, options = {}) {
    const {
      onOpen = null,
      onMessage = null,
      onError = null,
      onClose = null,
      events = {}, // 自定义事件处理器
      reconnect = true,
      reconnectInterval = 3000,
      maxReconnectAttempts = 5
    } = options

    // 如果已存在连接，先关闭
    if (this.connections.has(url)) {
      this.disconnect(url)
    }

    let reconnectAttempts = 0
    let reconnectTimer = null

    const createConnection = () => {
      console.log(`🔗 创建SSE连接: ${url}`)
      
      const eventSource = new EventSource(url)
      
      // 连接打开
      eventSource.onopen = (event) => {
        console.log(`✅ SSE连接已建立: ${url}`)
        reconnectAttempts = 0 // 重置重连次数
        if (onOpen) onOpen(event)
      }

      // 接收消息
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          if (onMessage) onMessage(data, event)
        } catch (error) {
          console.error('解析SSE消息失败:', error, event.data)
        }
      }

      // 处理自定义事件
      Object.keys(events).forEach(eventType => {
        eventSource.addEventListener(eventType, (event) => {
          try {
            const data = JSON.parse(event.data)
            events[eventType](data, event)
          } catch (error) {
            console.error(`解析SSE事件 [${eventType}] 失败:`, error, event.data)
          }
        })
      })

      // 连接错误
      eventSource.onerror = (event) => {
        console.error(`❌ SSE连接错误: ${url}`, event)
        
        if (eventSource.readyState === EventSource.CLOSED) {
          console.log(`🔌 SSE连接已关闭: ${url}`)
          this.connections.delete(url)
          
          if (onClose) onClose(event)
          
          // 自动重连
          if (reconnect && reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++
            console.log(`🔄 尝试重连SSE (${reconnectAttempts}/${maxReconnectAttempts}): ${url}`)
            
            reconnectTimer = setTimeout(() => {
              createConnection()
            }, reconnectInterval)
          } else if (reconnectAttempts >= maxReconnectAttempts) {
            console.error(`💥 SSE重连失败，已达到最大重试次数: ${url}`)
          }
        }
        
        if (onError) onError(event)
      }

      // 存储连接信息
      this.connections.set(url, {
        eventSource,
        reconnectTimer,
        options
      })

      return eventSource
    }

    return createConnection()
  }

  /**
   * 断开SSE连接
   * @param {string} url SSE接口地址
   */
  disconnect(url) {
    const connection = this.connections.get(url)
    if (connection) {
      console.log(`🔌 断开SSE连接: ${url}`)
      
      // 清除重连定时器
      if (connection.reconnectTimer) {
        clearTimeout(connection.reconnectTimer)
      }
      
      // 关闭连接
      if (connection.eventSource) {
        connection.eventSource.close()
      }
      
      this.connections.delete(url)
    }
  }

  /**
   * 断开所有SSE连接
   */
  disconnectAll() {
    console.log('🔌 断开所有SSE连接')
    for (const url of this.connections.keys()) {
      this.disconnect(url)
    }
  }

  /**
   * 获取连接状态
   * @param {string} url SSE接口地址
   * @returns {number} 连接状态
   */
  getConnectionState(url) {
    const connection = this.connections.get(url)
    if (connection && connection.eventSource) {
      return connection.eventSource.readyState
    }
    return EventSource.CLOSED
  }

  /**
   * 检查连接是否活跃
   * @param {string} url SSE接口地址
   * @returns {boolean} 是否活跃
   */
  isConnected(url) {
    return this.getConnectionState(url) === EventSource.OPEN
  }

  /**
   * 获取所有连接信息
   * @returns {Array} 连接信息列表
   */
  getAllConnections() {
    const connections = []
    for (const [url, connection] of this.connections.entries()) {
      connections.push({
        url,
        state: connection.eventSource ? connection.eventSource.readyState : EventSource.CLOSED,
        stateText: this.getStateText(connection.eventSource ? connection.eventSource.readyState : EventSource.CLOSED)
      })
    }
    return connections
  }

  /**
   * 获取连接状态文本
   * @param {number} state 连接状态
   * @returns {string} 状态文本
   */
  getStateText(state) {
    switch (state) {
      case EventSource.CONNECTING:
        return 'CONNECTING'
      case EventSource.OPEN:
        return 'OPEN'
      case EventSource.CLOSED:
        return 'CLOSED'
      default:
        return 'UNKNOWN'
    }
  }
}

// 创建全局SSE管理器实例
const sseManager = new SSEManager()

// 页面卸载时自动断开所有连接
window.addEventListener('beforeunload', () => {
  sseManager.disconnectAll()
})

export default sseManager
