package middleware

import (
	"server/core/dbpool"
	"server/model/response"
	"server/model/sys"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AdminAuth 管理员权限验证中间件
func AdminAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, exists := c.Get("claims")
		if !exists {
			response.NoAuth("未登录或非法访问", c)
			c.Abort()
			return
		}

		customClaims, ok := claims.(*sys.CustomClaims)
		if !ok {
			response.NoAuth("token解析失败", c)
			c.Abort()
			return
		}

		// 🚀 通过用户ID查询用户角色
		var user sys.SysUser
		if err := dbpool.ExecuteDBOperationAsyncAndWait("admin_auth_user_check", func(db *gorm.DB) error {
			return db.First(&user, customClaims.BaseClaims.ID).Error
		}); err != nil {
			response.NoAuth("用户不存在", c)
			c.Abort()
			return
		}

		// 检查是否是超级管理员
		if user.Role.RoleName != "superadmin" {
			response.NoAuth("权限不足，仅管理员可访问", c)
			c.Abort()
			return
		}

		c.Next()
	}
}
