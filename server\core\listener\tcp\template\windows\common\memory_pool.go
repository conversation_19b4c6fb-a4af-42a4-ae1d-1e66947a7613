//go:build windows
// +build windows

package common

import (
	"sync"
)

// MemoryPool Windows客户端内存池管理
type MemoryPool struct {
	// 网络缓冲区池 - 不同大小的缓冲区
	smallBufferPool  sync.Pool // 1KB - 用于小数据读写
	mediumBufferPool sync.Pool // 4KB - 用于常规网络读写
	largeBufferPool  sync.Pool // 32KB - 用于大数据传输
	
	// TLV数据包池
	packetPool       sync.Pool // TLV数据包对象
	headerPool       sync.Pool // TLV头部对象
	
	// 截图相关缓冲区池
	imageBufferPool  sync.Pool // 图像数据缓冲区
	compressPool     sync.Pool // 压缩缓冲区
	
	// 字节切片池 - 用于临时数据处理
	byteSlicePool    sync.Pool // []byte切片
}

// NewMemoryPool 创建Windows客户端内存池
func NewMemoryPool() *MemoryPool {
	mp := &MemoryPool{
		// 小缓冲区池 (1KB)
		smallBufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 1024)
			},
		},
		// 中等缓冲区池 (4KB)
		mediumBufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 4096)
			},
		},
		// 大缓冲区池 (32KB)
		largeBufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 32768)
			},
		},
		// TLV数据包池
		packetPool: sync.Pool{
			New: func() interface{} {
				return &Packet{}
			},
		},
		// TLV头部池
		headerPool: sync.Pool{
			New: func() interface{} {
				return &Header{}
			},
		},
		// 图像缓冲区池 (1MB)
		imageBufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 1024*1024)
			},
		},
		// 压缩缓冲区池 (512KB)
		compressPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 512*1024)
			},
		},
		// 字节切片池
		byteSlicePool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 0, 1024)
			},
		},
	}
	return mp
}

// GetSmallBuffer 获取小缓冲区 (1KB)
func (mp *MemoryPool) GetSmallBuffer() []byte {
	return mp.smallBufferPool.Get().([]byte)
}

// PutSmallBuffer 归还小缓冲区
func (mp *MemoryPool) PutSmallBuffer(buf []byte) {
	if cap(buf) == 1024 {
		mp.smallBufferPool.Put(buf[:1024])
	}
}

// GetMediumBuffer 获取中等缓冲区 (4KB)
func (mp *MemoryPool) GetMediumBuffer() []byte {
	return mp.mediumBufferPool.Get().([]byte)
}

// PutMediumBuffer 归还中等缓冲区
func (mp *MemoryPool) PutMediumBuffer(buf []byte) {
	if cap(buf) == 4096 {
		mp.mediumBufferPool.Put(buf[:4096])
	}
}

// GetLargeBuffer 获取大缓冲区 (32KB)
func (mp *MemoryPool) GetLargeBuffer() []byte {
	return mp.largeBufferPool.Get().([]byte)
}

// PutLargeBuffer 归还大缓冲区
func (mp *MemoryPool) PutLargeBuffer(buf []byte) {
	if cap(buf) == 32768 {
		mp.largeBufferPool.Put(buf[:32768])
	}
}

// GetPacket 获取TLV数据包
func (mp *MemoryPool) GetPacket() *Packet {
	packet := mp.packetPool.Get().(*Packet)
	// 重置数据包
	packet.Header = nil
	packet.PacketData = nil
	return packet
}

// PutPacket 归还TLV数据包
func (mp *MemoryPool) PutPacket(packet *Packet) {
	if packet != nil {
		// 清理数据包内容
		packet.Header = nil
		packet.PacketData = nil
		mp.packetPool.Put(packet)
	}
}

// GetHeader 获取TLV头部
func (mp *MemoryPool) GetHeader() *Header {
	header := mp.headerPool.Get().(*Header)
	// 重置头部
	*header = Header{}
	return header
}

// PutHeader 归还TLV头部
func (mp *MemoryPool) PutHeader(header *Header) {
	if header != nil {
		mp.headerPool.Put(header)
	}
}

// GetImageBuffer 获取图像缓冲区 (1MB)
func (mp *MemoryPool) GetImageBuffer() []byte {
	return mp.imageBufferPool.Get().([]byte)
}

// PutImageBuffer 归还图像缓冲区
func (mp *MemoryPool) PutImageBuffer(buf []byte) {
	if cap(buf) >= 1024*1024 {
		mp.imageBufferPool.Put(buf[:1024*1024])
	}
}

// GetCompressBuffer 获取压缩缓冲区 (512KB)
func (mp *MemoryPool) GetCompressBuffer() []byte {
	return mp.compressPool.Get().([]byte)
}

// PutCompressBuffer 归还压缩缓冲区
func (mp *MemoryPool) PutCompressBuffer(buf []byte) {
	if cap(buf) >= 512*1024 {
		mp.compressPool.Put(buf[:512*1024])
	}
}

// GetByteSlice 获取字节切片
func (mp *MemoryPool) GetByteSlice() []byte {
	return mp.byteSlicePool.Get().([]byte)[:0]
}

// PutByteSlice 归还字节切片
func (mp *MemoryPool) PutByteSlice(slice []byte) {
	if cap(slice) >= 1024 && cap(slice) <= 32768 {
		mp.byteSlicePool.Put(slice[:0])
	}
}

// GetBufferBySize 根据大小获取合适的缓冲区
func (mp *MemoryPool) GetBufferBySize(size int) []byte {
	switch {
	case size <= 1024:
		return mp.GetSmallBuffer()[:size]
	case size <= 4096:
		return mp.GetMediumBuffer()[:size]
	case size <= 32768:
		return mp.GetLargeBuffer()[:size]
	default:
		// 对于超大缓冲区，直接分配
		return make([]byte, size)
	}
}

// PutBufferBySize 根据大小归还缓冲区
func (mp *MemoryPool) PutBufferBySize(buf []byte) {
	switch cap(buf) {
	case 1024:
		mp.PutSmallBuffer(buf)
	case 4096:
		mp.PutMediumBuffer(buf)
	case 32768:
		mp.PutLargeBuffer(buf)
	default:
		// 超大缓冲区不回收，让GC处理
	}
}
