package sys

import (
	"github.com/gin-gonic/gin"
)

type LogRoute struct{}

func (r *LogRoute) InitLogRoute(Router *gin.RouterGroup) (IR gin.IRoutes) {
	logRouter := Router.Group("/log")
	{
		logRouter.GET("/files", logApi.GetLogFileList)        // 获取日志文件列表
		logRouter.GET("/content", logApi.GetLogContent)       // 获取日志文件内容
		logRouter.GET("/stream", logApi.StreamLogContent)     // 实时日志流
		logRouter.GET("/stats", logApi.GetLogStats)           // 获取日志统计信息
		logRouter.GET("/download", logApi.DownloadLogFile)    // 下载日志文件
	}
	return logRouter
}
