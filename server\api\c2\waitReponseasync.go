package c2

import (
	"fmt"
	"server/core/manager"
	"server/core/workerpool"
	"time"

	"server/global"
	"server/model/response"
	"server/utils"


	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// waitForResponseAsync 异步等待响应，避免阻塞主线程
func waitForResponseAsyncWithClientId(ctx *gin.Context, taskID uint64, operation string, clientIDStr string) {
	// 创建响应通道
	responseChan := make(chan interface{}, 1)
	errorChan := make(chan error, 1)

	// 提交等待任务到工作池
	task := workerpool.NewNetworkTask("wait_response_"+operation, func() error {
		result, err := manager.ResponseMgr.WaitForResponse(taskID, 30*time.Second)
		if err != nil {
			errorChan <- err
		} else {
			responseChan <- result
		}
		return nil
	})
	workerpool.SubmitNetworkTask(task)

	// 等待响应或超时
	select {
	case result := <-responseChan:
		utils.UpdateTaskStatus(taskID, "completed", "")
		global.LOG.Info(operation+"成功", zap.String("clientID", clientIDStr))
		response.OkWithData(result, ctx)
	case err := <-errorChan:
		response.ErrorWithMessage(fmt.Sprintf(operation+"失败: %v", err), ctx)
	case <-time.After(35 * time.Second): // 比工作池超时稍长一点
		response.ErrorWithMessage(operation+"超时", ctx)
	}
}

func waitForResponseAsync(ctx *gin.Context, taskID uint64, operation string) {
	// 创建响应通道
	responseChan := make(chan interface{}, 1)
	errorChan := make(chan error, 1)

	// 提交等待任务到工作池
	task := workerpool.NewNetworkTask("wait_response_"+operation, func() error {
		result, err := manager.ResponseMgr.WaitForResponse(taskID, 30*time.Second)
		if err != nil {
			errorChan <- err
		} else {
			responseChan <- result
		}
		return nil
	})
	workerpool.SubmitNetworkTask(task)

	// 等待响应或超时
	select {
	case result := <-responseChan:
		utils.UpdateTaskStatus(taskID, "completed", "")
		global.LOG.Info(operation+"成功")
		response.OkWithData(result, ctx)
	case err := <-errorChan:
		response.ErrorWithMessage(fmt.Sprintf(operation+"失败: %v", err), ctx)
	case <-time.After(35 * time.Second): // 比工作池超时稍长一点
		response.ErrorWithMessage(operation+"超时", ctx)
	}
}