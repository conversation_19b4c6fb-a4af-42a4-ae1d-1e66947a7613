package tcp

import (
	"fmt"
	"server/core/dbpool"
	"server/global"
	"server/model/sys"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InitTCPListeners 初始化所有状态为启用的tcp类型监听器
func InitTCPListeners() {
	// 🚀 从数据库中获取所有状态为启用的tcp类型监听器
	var listeners []sys.Listener
	if err := dbpool.ExecuteDBOperationAsyncAndWait("tcp_listeners_init", func(db *gorm.DB) error {
		return db.Where("type = ? AND status = ?", "tcp", 1).Find(&listeners).Error
	}); err != nil {
		global.LOG.Error("获取tcp监听器列表失败", zap.Error(err))
		return
	}

	// 启动所有监听器
	for _, l := range listeners {
		TCPManager.StartListener(l)
	}

	global.LOG.Info(fmt.Sprintf("已启动 %d 个tcp监听器", len(listeners)))
}
