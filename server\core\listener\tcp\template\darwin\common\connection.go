//go:build darwin
// +build darwin
package common

import (
	"bufio"
	"context"
	"encoding/binary"
	"fmt"
	"io"
	"log"
	"net"
	"sync"
	"time"

	"github.com/creack/pty"
)

func (cm *ConnectionManager) connect() error {
	conn, err := net.DialTimeout("tcp", cm.config.ServerAddr, 10*time.Second)
	var tcpConn *net.TCPConn
	var ok bool
	if tcpConn, ok = conn.(*net.TCPConn); ok {
		// 禁用Nagle
		err = tcpConn.SetNoDelay(true)
		if err != nil {
			log.Println("SetNoDelay失败: ", err)
		}
		// 增大写缓冲
		err = tcpConn.SetWriteBuffer(64 * 1024)
		if err != nil {
			log.Println("SetWriteBuffer失败: ", err)
		}
		// 增大读缓冲
		err = tcpConn.SetReadBuffer(64 * 1024)
		if err != nil {
			log.Println("SetReadBuffer失败: ", err)
		}
		// 设置保活参数
		if err = tcpConn.SetKeepAlive(true); err != nil {
			log.Println("SetKeepAlive: ", err)
		}

		if err = tcpConn.SetKeepAlivePeriod(30 * time.Second); err != nil {
			log.Println("SetKeepAlivePeriod: ", err)
		}
	}
	if err != nil {
		return fmt.Errorf("连接失败: %v", err)
	}

	cm.mu.Lock()
	cm.conn = tcpConn
	cm.retryCount = 0
	cm.mu.Unlock()

	log.Printf("已连接到服务器: %s", cm.config.ServerAddr)

	// 发送注册包
	regPacket, err := cm.CreateRegistrationPacket()
	if err != nil {
		return fmt.Errorf("创建注册包失败: %v", err)
	}

	regBytes := regPacket.Serialize()

	if _, err = conn.Write(regBytes); err != nil {
		return fmt.Errorf("发送注册包失败: %v", err)
	}

	log.Println("已发送注册包")

	// 等待注册响应
	if err := cm.waitForRegistrationResponse(); err != nil {
		return fmt.Errorf("等待注册响应失败: %v", err)
	}

	log.Println("注册成功，连接建立完成")
	return nil
}

func (cm *ConnectionManager) readNetworkData(ctx context.Context) {

	reader := bufio.NewReader(cm.conn)

	for {
		select {
		case <-ctx.Done():
			return
		default:
			packetBytes, err := cm.readSinglePacket(reader)
			if err != nil {
				// 如果读取出错（网络断开），记录日志并直接返回
				if err != io.EOF && err != io.ErrUnexpectedEOF {
					log.Printf("读取数据包时网络错误: %v", err)
				} else {
					log.Println("网络连接已由对端关闭。")
				}
				return
			}
			// 处理数据包
			packet, complete, err := cm.ProcessIncomingPacket(packetBytes)
			if err != nil {
				log.Printf("处理数据包失败: %v", err)
				continue
			}
			if !complete {
				continue
			}

			// 处理数据包内容
			select {
			case <-ctx.Done():
				return // 收到退出信号，立即返回
			default:
				// 否则，继续处理包
				cm.processPacket(packet)
			}
		}
	}
}

// 新增辅助函数，封装读取单个包的逻辑（使用内存池优化）
func (cm *ConnectionManager) readSinglePacket(reader *bufio.Reader) ([]byte, error) {
	// 1. 读取8字节前缀 - 使用小缓冲区
	prefixBytes := cm.memoryPool.GetSmallBuffer()[:8]
	defer cm.memoryPool.PutSmallBuffer(prefixBytes[:1024]) // 归还完整缓冲区

	if _, err := io.ReadFull(reader, prefixBytes); err != nil {
		return nil, err
	}

	// 2. 读取固定长度的Header - 使用小缓冲区
	headerBytes := cm.memoryPool.GetSmallBuffer()[:HeaderSize]
	defer cm.memoryPool.PutSmallBuffer(headerBytes[:1024]) // 归还完整缓冲区

	if _, err := io.ReadFull(reader, headerBytes); err != nil {
		return nil, err
	}
	HeaderType := headerBytes[0]
	if HeaderType >= 13 {
		log.Printf("错误的HeaderType: %v", HeaderType)
		return nil, fmt.Errorf("错误的HeaderType: %v", HeaderType)
	}

	// 3. 从Header解析出长度
	length := binary.BigEndian.Uint32(headerBytes[12:16]) // 假设Length字段在这个位置
	if length > 1024*1024*1024 {                          // 您的健全性检查
		return nil, fmt.Errorf("数据包过大: %d bytes", length)
	}

	// 4. 读取剩余的Body - 使用内存池获取合适大小的缓冲区
	bodyLength := length - HeaderSize
	bodyBytes := cm.memoryPool.GetBufferBySize(int(bodyLength))
	defer cm.memoryPool.PutBufferBySize(bodyBytes)

	if _, err := io.ReadFull(reader, bodyBytes); err != nil {
		return nil, err
	}

	// 5. 组合成一个完整的带前缀的包 - 使用内存池
	totalSize := int(8 + length)
	fullPacket := cm.memoryPool.GetBufferBySize(totalSize)
	// 注意：这里不defer归还，因为要返回给调用者使用

	copy(fullPacket[:8], prefixBytes)
	copy(fullPacket[8:8+HeaderSize], headerBytes)
	copy(fullPacket[8+HeaderSize:], bodyBytes)

	// 创建最终返回的切片（调用者负责管理）
	result := make([]byte, totalSize)
	copy(result, fullPacket[:totalSize])
	cm.memoryPool.PutBufferBySize(fullPacket) // 归还临时缓冲区

	return result, nil
}

func (cm *ConnectionManager) handleConnection() error {
	defer cm.conn.Close()

	// 创建读写协程
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	defer cm.cleanup()
	// 初始化通道
	cm.ptyOutputChan = make(chan []byte, 100)
	cm.ptyInputChan = make(chan []byte, 100)
	cm.resizeChan = make(chan *pty.Winsize, 10)
	if err := cm.startPTY(ctx); err != nil {
		return err
	}
	var wg sync.WaitGroup
	wg.Add(5) // 添加心跳检测goroutine

	// 读取网络数据协程
	go func() {
		defer wg.Done()
		defer cancel()
		cm.readNetworkData(ctx)
		log.Println("readNetworkData goroutine 退出")
	}()

	// 发送PTY输出协程
	go func() {
		defer wg.Done()
		cm.sendPTYOutput(ctx)
		log.Println("sendPTYOutput goroutine 退出")
	}()
	go func() {
		defer wg.Done()
		defer cancel()
		cm.readPTYOutput(ctx)
		log.Println("readPTYOutput goroutine 退出")
	}()
	go func() {
		defer wg.Done()
		cm.handlePTYInput(ctx)
		log.Println("handlePTYInput goroutine 退出")
	}()

	// Linux客户端主动心跳检测
	go func() {
		defer wg.Done()
		cm.startClientHeartbeat(ctx)
		log.Println("Linux clientHeartbeat goroutine 退出")
	}()

	go func() {
		err := cm.cmd.Wait() // 阻塞直到/bin/bash进程退出
		log.Printf("PTY底层进程已退出，错误: %v", err)
		cancel() // 进程退出，立刻取消所有其他操作
	}()
	<-ctx.Done()
	log.Println("Context cancelled, 等待所有goroutines结束...")
	wg.Wait()
	return nil
}

// waitForRegistrationResponse 等待服务器的注册响应
func (cm *ConnectionManager) waitForRegistrationResponse() error {
	reader := bufio.NewReader(cm.conn)

	// 设置超时时间
	cm.conn.SetReadDeadline(time.Now().Add(30 * time.Second))
	defer cm.conn.SetReadDeadline(time.Time{}) // 清除超时设置

	for {
		packetBytes, err := cm.readSinglePacket(reader)
		if err != nil {
			return fmt.Errorf("读取注册响应失败: %v", err)
		}
		// 解析数据包 - 使用内存池
		packet := cm.memoryPool.GetPacket()
		defer cm.memoryPool.PutPacket(packet)

		if err := packet.DeserializePacket(packetBytes); err != nil {
			return fmt.Errorf("解析注册响应包失败: %v", err)
		}

		// 解密数据包
		if err := packet.DecryptPacket(cm.metadata); err != nil {
			return fmt.Errorf("解密注册响应包失败: %v", err)
		}

		// 检查是否为注册响应
		if packet.Header.Type == Registration && packet.Header.Code == RegResponse {
			log.Println("收到注册响应，注册成功!")
			return nil
		}

		// 如果不是注册响应，继续等待
		log.Printf("收到非注册响应包: Type=%d, Code=%d，继续等待...", packet.Header.Type, packet.Header.Code)
	}
}

func (cm *ConnectionManager) sendResp(Type uint8, Code uint8, resp interface{}) {
	log.Printf("📡 sendResp: 开始发送响应 Type=%d, Code=%d", Type, Code)

	// 检查连接状态，防止空指针异常
	cm.mu.Lock()
	conn := cm.conn
	cm.mu.Unlock()

	if conn == nil {
		log.Printf("❌ sendResp: 连接已断开，无法发送响应: Type=%d, Code=%d", Type, Code)
		return
	}
	log.Printf("✅ sendResp: 连接状态正常")

	log.Printf("🔧 sendResp: 开始序列化响应数据...")
	buf, err := cm.serializer.Serialize(resp)
	if err != nil {
		log.Printf("❌ sendResp: 序列化响应失败: %v", err)
		return
	}
	log.Printf("✅ sendResp: 序列化成功，数据大小=%d字节", len(buf))

	log.Printf("🔧 sendResp: 开始创建数据包...")
	packets, err := cm.CreatePackets(buf, Type, Code)
	if err != nil {
		log.Printf("❌ sendResp: 创建数据包失败: %v", err)
		return
	}
	log.Printf("✅ sendResp: 创建数据包成功，包数量=%d", len(packets))

	log.Printf("📤 sendResp: 开始发送数据包...")
	for i, packet := range packets {
		packetBytes := packet.Serialize()
		log.Printf("📤 sendResp: 发送第%d个包，大小=%d字节", i+1, len(packetBytes))

		if _, err := conn.Write(packetBytes); err != nil {
			log.Printf("❌ sendResp: 发送第%d个包失败: %v", i+1, err)
			return
		}
		log.Printf("✅ sendResp: 第%d个包发送成功", i+1)
	}
	log.Printf("🎉 sendResp: 所有数据包发送完成")
}
