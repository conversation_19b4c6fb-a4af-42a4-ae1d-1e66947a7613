package workerpool

import (
	"context"
	"runtime"
	"runtime/pprof"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
	"server/global"
)

// LeakDetector Goroutine泄漏检测器
type LeakDetector struct {
	// 配置参数
	checkInterval     time.Duration
	thresholdIncrease int
	maxGoroutines     int
	
	// 运行时状态
	ctx               context.Context
	cancel            context.CancelFunc
	mu                sync.RWMutex
	isRunning         bool
	
	// 统计信息
	stats             *LeakStats
	history           []GoroutineSnapshot
	maxHistorySize    int
	
	// 回调函数
	onLeakDetected    func(*LeakInfo)
	onThresholdExceeded func(int, int)
}

// LeakStats 泄漏检测统计信息
type LeakStats struct {
	StartTime           time.Time `json:"start_time"`
	LastCheckTime       time.Time `json:"last_check_time"`
	TotalChecks         int64     `json:"total_checks"`
	LeaksDetected       int64     `json:"leaks_detected"`
	MaxGoroutines       int       `json:"max_goroutines"`
	MinGoroutines       int       `json:"min_goroutines"`
	CurrentGoroutines   int       `json:"current_goroutines"`
	AverageGoroutines   float64   `json:"average_goroutines"`
	ThresholdExceeded   int64     `json:"threshold_exceeded"`
	mu                  sync.RWMutex
}

// GoroutineSnapshot Goroutine快照
type GoroutineSnapshot struct {
	Timestamp   time.Time `json:"timestamp"`
	Count       int       `json:"count"`
	StackTraces []string  `json:"stack_traces,omitempty"`
}

// LeakInfo 泄漏信息
type LeakInfo struct {
	DetectedAt      time.Time         `json:"detected_at"`
	GoroutineCount  int               `json:"goroutine_count"`
	Increase        int               `json:"increase"`
	SuspiciousStacks []string         `json:"suspicious_stacks"`
	Recommendation  string            `json:"recommendation"`
}

// LeakDetectorConfig 泄漏检测器配置
type LeakDetectorConfig struct {
	CheckInterval       time.Duration
	ThresholdIncrease   int
	MaxGoroutines       int
	MaxHistorySize      int
	OnLeakDetected      func(*LeakInfo)
	OnThresholdExceeded func(current, max int)
}

// NewLeakDetector 创建新的泄漏检测器
func NewLeakDetector(config LeakDetectorConfig) *LeakDetector {
	ctx, cancel := context.WithCancel(context.Background())
	
	// 设置默认值
	if config.CheckInterval <= 0 {
		config.CheckInterval = 30 * time.Second
	}
	if config.ThresholdIncrease <= 0 {
		config.ThresholdIncrease = 100
	}
	if config.MaxGoroutines <= 0 {
		config.MaxGoroutines = 10000
	}
	if config.MaxHistorySize <= 0 {
		config.MaxHistorySize = 100
	}
	
	detector := &LeakDetector{
		checkInterval:       config.CheckInterval,
		thresholdIncrease:   config.ThresholdIncrease,
		maxGoroutines:       config.MaxGoroutines,
		ctx:                 ctx,
		cancel:              cancel,
		maxHistorySize:      config.MaxHistorySize,
		onLeakDetected:      config.OnLeakDetected,
		onThresholdExceeded: config.OnThresholdExceeded,
		stats: &LeakStats{
			StartTime:         time.Now(),
			MinGoroutines:     runtime.NumGoroutine(),
			CurrentGoroutines: runtime.NumGoroutine(),
		},
		history: make([]GoroutineSnapshot, 0, config.MaxHistorySize),
	}
	
	return detector
}

// Start 启动泄漏检测
func (ld *LeakDetector) Start() {
	ld.mu.Lock()
	defer ld.mu.Unlock()
	
	if ld.isRunning {
		return
	}
	
	ld.isRunning = true
	go ld.monitor()
	
	global.LOG.Info("Goroutine泄漏检测器已启动",
		zap.Duration("checkInterval", ld.checkInterval),
		zap.Int("thresholdIncrease", ld.thresholdIncrease),
		zap.Int("maxGoroutines", ld.maxGoroutines))
}

// Stop 停止泄漏检测
func (ld *LeakDetector) Stop() {
	ld.mu.Lock()
	defer ld.mu.Unlock()
	
	if !ld.isRunning {
		return
	}
	
	ld.cancel()
	ld.isRunning = false
	
	global.LOG.Info("Goroutine泄漏检测器已停止")
}

// monitor 监控goroutine数量
func (ld *LeakDetector) monitor() {
	ticker := time.NewTicker(ld.checkInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			ld.checkGoroutines()
		case <-ld.ctx.Done():
			return
		}
	}
}

// checkGoroutines 检查goroutine数量
func (ld *LeakDetector) checkGoroutines() {
	currentCount := runtime.NumGoroutine()
	now := time.Now()
	
	ld.stats.mu.Lock()
	ld.stats.LastCheckTime = now
	ld.stats.TotalChecks++
	ld.stats.CurrentGoroutines = currentCount
	
	// 更新最大最小值
	if currentCount > ld.stats.MaxGoroutines {
		ld.stats.MaxGoroutines = currentCount
	}
	if currentCount < ld.stats.MinGoroutines {
		ld.stats.MinGoroutines = currentCount
	}
	
	// 计算平均值
	if ld.stats.TotalChecks > 0 {
		ld.stats.AverageGoroutines = (ld.stats.AverageGoroutines*float64(ld.stats.TotalChecks-1) + float64(currentCount)) / float64(ld.stats.TotalChecks)
	}
	ld.stats.mu.Unlock()
	
	// 检查是否超过最大限制
	if currentCount > ld.maxGoroutines {
		ld.stats.mu.Lock()
		ld.stats.ThresholdExceeded++
		ld.stats.mu.Unlock()
		
		global.LOG.Warn("Goroutine数量超过最大限制",
			zap.Int("current", currentCount),
			zap.Int("max", ld.maxGoroutines))
		
		if ld.onThresholdExceeded != nil {
			ld.onThresholdExceeded(currentCount, ld.maxGoroutines)
		}
	}
	
	// 检查是否有泄漏
	if ld.detectLeak(currentCount) {
		leakInfo := ld.generateLeakInfo(currentCount)
		
		ld.stats.mu.Lock()
		ld.stats.LeaksDetected++
		ld.stats.mu.Unlock()
		
		global.LOG.Error("检测到Goroutine泄漏",
			zap.Int("current", currentCount),
			zap.Int("increase", leakInfo.Increase),
			zap.Strings("suspiciousStacks", leakInfo.SuspiciousStacks))
		
		if ld.onLeakDetected != nil {
			ld.onLeakDetected(leakInfo)
		}
	}
	
	// 记录快照
	ld.recordSnapshot(currentCount, false)
}

// detectLeak 检测是否有泄漏
func (ld *LeakDetector) detectLeak(currentCount int) bool {
	ld.mu.RLock()
	defer ld.mu.RUnlock()
	
	if len(ld.history) < 2 {
		return false
	}
	
	// 获取最近的快照
	lastSnapshot := ld.history[len(ld.history)-1]
	
	// 计算增长量
	increase := currentCount - lastSnapshot.Count
	
	// 如果增长超过阈值，认为可能有泄漏
	return increase > ld.thresholdIncrease
}

// generateLeakInfo 生成泄漏信息
func (ld *LeakDetector) generateLeakInfo(currentCount int) *LeakInfo {
	ld.mu.RLock()
	lastCount := 0
	if len(ld.history) > 0 {
		lastCount = ld.history[len(ld.history)-1].Count
	}
	ld.mu.RUnlock()
	
	increase := currentCount - lastCount
	suspiciousStacks := ld.getSuspiciousStacks()
	
	return &LeakInfo{
		DetectedAt:       time.Now(),
		GoroutineCount:   currentCount,
		Increase:         increase,
		SuspiciousStacks: suspiciousStacks,
		Recommendation:   ld.generateRecommendation(suspiciousStacks),
	}
}

// getSuspiciousStacks 获取可疑的堆栈信息
func (ld *LeakDetector) getSuspiciousStacks() []string {
	var stacks []string
	
	// 获取所有goroutine的堆栈信息
	profiles := pprof.Profiles()
	for _, profile := range profiles {
		if profile.Name() == "goroutine" {
			// 这里简化处理，实际应该解析profile数据
			stacks = append(stacks, "goroutine profile available")
			break
		}
	}
	
	return stacks
}

// generateRecommendation 生成建议
func (ld *LeakDetector) generateRecommendation(stacks []string) string {
	recommendations := []string{
		"检查是否有未关闭的goroutine",
		"确认所有的context都正确取消",
		"检查是否有阻塞的channel操作",
		"验证所有的定时器都被正确停止",
		"检查是否有循环引用导致的内存泄漏",
	}
	
	// 根据堆栈信息提供更具体的建议
	for _, stack := range stacks {
		if strings.Contains(stack, "channel") {
			return "检测到channel相关操作，请确认channel操作没有阻塞"
		}
		if strings.Contains(stack, "timer") {
			return "检测到定时器相关操作，请确认定时器被正确停止"
		}
		if strings.Contains(stack, "http") {
			return "检测到HTTP相关操作，请确认HTTP连接被正确关闭"
		}
	}
	
	return strings.Join(recommendations, "; ")
}

// recordSnapshot 记录快照
func (ld *LeakDetector) recordSnapshot(count int, includeStacks bool) {
	ld.mu.Lock()
	defer ld.mu.Unlock()
	
	snapshot := GoroutineSnapshot{
		Timestamp: time.Now(),
		Count:     count,
	}
	
	if includeStacks {
		snapshot.StackTraces = ld.getSuspiciousStacks()
	}
	
	ld.history = append(ld.history, snapshot)
	
	// 保持历史记录大小
	if len(ld.history) > ld.maxHistorySize {
		ld.history = ld.history[1:]
	}
}

// GetStats 获取统计信息
func (ld *LeakDetector) GetStats() *LeakStats {
	ld.stats.mu.RLock()
	defer ld.stats.mu.RUnlock()
	
	// 复制统计信息
	stats := &LeakStats{
		StartTime:           ld.stats.StartTime,
		LastCheckTime:       ld.stats.LastCheckTime,
		TotalChecks:         ld.stats.TotalChecks,
		LeaksDetected:       ld.stats.LeaksDetected,
		MaxGoroutines:       ld.stats.MaxGoroutines,
		MinGoroutines:       ld.stats.MinGoroutines,
		CurrentGoroutines:   ld.stats.CurrentGoroutines,
		AverageGoroutines:   ld.stats.AverageGoroutines,
		ThresholdExceeded:   ld.stats.ThresholdExceeded,
	}
	
	return stats
}

// GetHistory 获取历史快照
func (ld *LeakDetector) GetHistory() []GoroutineSnapshot {
	ld.mu.RLock()
	defer ld.mu.RUnlock()
	
	// 复制历史记录
	history := make([]GoroutineSnapshot, len(ld.history))
	copy(history, ld.history)
	
	return history
}

// 全局泄漏检测器
var GlobalLeakDetector *LeakDetector

// InitGlobalLeakDetector 初始化全局泄漏检测器
func InitGlobalLeakDetector() {
	GlobalLeakDetector = NewLeakDetector(LeakDetectorConfig{
		CheckInterval:     30 * time.Second,
		ThresholdIncrease: 50,
		MaxGoroutines:     5000,
		MaxHistorySize:    100,
		OnLeakDetected: func(info *LeakInfo) {
			global.LOG.Error("检测到Goroutine泄漏",
				zap.Time("detectedAt", info.DetectedAt),
				zap.Int("goroutineCount", info.GoroutineCount),
				zap.Int("increase", info.Increase),
				zap.String("recommendation", info.Recommendation))
		},
		OnThresholdExceeded: func(current, max int) {
			global.LOG.Warn("Goroutine数量超过阈值",
				zap.Int("current", current),
				zap.Int("max", max))
		},
	})
	
	GlobalLeakDetector.Start()
}
