package sys

import (
	"errors"
	"server/core/dbpool"
	"server/global"
	"server/model/request"
	"server/model/sys"
	"server/utils"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type UserService struct{}

var UserServiceApp = new(UserService)

func (userService *UserService) Login(u *sys.SysUser) (userInter *sys.SysUser, err error) {

	var user sys.SysUser
	// 🚀 使用数据库连接池进行查询操作，保持同步以确保登录的实时性
	err = dbpool.ExecuteDBOperationAsyncAndWait("user_login_query", func(db *gorm.DB) error {
		return db.Where("username = ?", u.Username).First(&user).Error
	})

	if err == nil {
		if ok := utils.BcryptCheck(u.Password, user.Password); !ok {
			return nil, errors.New("密码错误")
		}
	}
	return &user, err
}

// UpdateUserInfo 更新用户信息
func (userService *UserService) UpdateUserInfo(uuid string, req request.UpdateUserInfoReq) (user *sys.SysUser, err error) {
	// 查找用户
	var sysUser sys.SysUser
	// 🚀 使用数据库连接池查询用户
	if err = dbpool.ExecuteDBOperationAsyncAndWait("user_query_by_uuid", func(db *gorm.DB) error {
		return db.Where("uuid = ?", uuid).First(&sysUser).Error
	}); err != nil {
		return nil, errors.New("用户不存在")
	}

	// 更新用户名
	sysUser.Username = req.Username

	// 如果提供了密码，则更新密码
	if req.Password != "" {
		hashedPassword := utils.BcryptHash(req.Password)

		sysUser.Password = hashedPassword
	}

	// 🚀 保存更新
	if err = dbpool.ExecuteDBOperationAsyncAndWait("user_info_update", func(db *gorm.DB) error {
		return db.Save(&sysUser).Error
	}); err != nil {
		return nil, errors.New("更新用户信息失败")
	}

	// 如果是超级管理员用户，同步更新配置文件中的管理员信息
	if sysUser.Role.RoleName == "superadmin" {
		// 更新全局配置中的管理员信息
		global.CONFIG.Admin.Username = sysUser.Username
		if req.Password != "" {
			global.CONFIG.Admin.Password = req.Password // 保存明文密码到配置
		}

		// 将更新后的配置写回配置文件
		if global.VP != nil {
			// 更新Viper中的值
			global.VP.Set("admin.username", sysUser.Username)
			if req.Password != "" {
				global.VP.Set("admin.password", req.Password)
			}

			// 保存到配置文件
			err = global.VP.WriteConfig()
			if err != nil {
				global.LOG.Error("更新配置文件失败", zap.Error(err))
			} else {
				global.LOG.Info("已同步更新管理员信息到配置文件")
			}
		}
	}

	return &sysUser, nil
}

// GetUserList 获取用户列表
func (userService *UserService) GetUserList(searchInfo request.UserSearch) (list []sys.SysUser, total int64, err error) {
	limit := searchInfo.PageSize
	offset := searchInfo.PageSize * (searchInfo.Page - 1)

	// 🚀 使用数据库连接池进行查询操作
	err = dbpool.ExecuteDBOperationAsyncAndWait("user_list_query", func(db *gorm.DB) error {
		query := db.Model(&sys.SysUser{})

		// 构建查询条件
		if searchInfo.Username != "" {
			query = query.Where("username LIKE ?", "%"+searchInfo.Username+"%")
		}
		if searchInfo.RoleName != "" {
			query = query.Where("role_name = ?", searchInfo.RoleName)
		}
		if searchInfo.Enable != nil {
			query = query.Where("enable = ?", *searchInfo.Enable)
		}

		// 获取总数
		if err := query.Count(&total).Error; err != nil {
			return err
		}

		// 获取列表
		return query.Limit(limit).Offset(offset).Order("id desc").Find(&list).Error
	})

	return list, total, err
}

// CreateUser 创建用户
func (userService *UserService) CreateUser(req request.CreateUserRequest) error {
	// 禁止创建超级管理员
	if req.RoleName == "superadmin" {
		return errors.New("不允许创建超级管理员")
	}

	// 🚀 使用数据库连接池进行事务操作，确保数据一致性
	return dbpool.ExecuteDBTransaction("user_create", func(db *gorm.DB) error {
		// 检查用户名是否已存在
		var existUser sys.SysUser
		if err := db.Where("username = ?", req.Username).First(&existUser).Error; err == nil {
			return errors.New("用户名已存在")
		}

		// 设置默认启用状态
		if req.Enable == 0 {
			req.Enable = 1
		}

		// 创建新用户
		newUser := sys.SysUser{
			UUID:     utils.GenerateUUID(),
			Username: req.Username,
			Password: utils.BcryptHash(req.Password),
			Role: sys.Role{
				RoleName: req.RoleName,
			},
			Enable: req.Enable,
		}

		return db.Create(&newUser).Error
	})
}

// UpdateUser 更新用户
func (userService *UserService) UpdateUser(req request.UpdateUserRequest) error {
	// 🚀 使用数据库连接池进行事务操作，确保更新的原子性
	return dbpool.ExecuteDBTransaction("user_update", func(db *gorm.DB) error {
		// 检查用户是否存在
		var user sys.SysUser
		if err := db.First(&user, req.ID).Error; err != nil {
			return errors.New("用户不存在")
		}

		// 检查是否是超级管理员
		if user.Role.RoleName == "superadmin" {
			return errors.New("不能修改超级管理员")
		}

		// 禁止修改为超级管理员
		if req.RoleName == "superadmin" {
			return errors.New("不允许将用户修改为超级管理员")
		}

		// 检查用户名是否被其他用户使用
		if req.Username != "" && req.Username != user.Username {
			var existUser sys.SysUser
			if err := db.Where("username = ? AND id != ?", req.Username, req.ID).First(&existUser).Error; err == nil {
				return errors.New("用户名已被使用")
			}
			user.Username = req.Username
		}

		// 更新密码
		if req.Password != "" {
			user.Password = utils.BcryptHash(req.Password)
		}

		// 更新角色
		if req.RoleName != "" {
			user.Role.RoleName = req.RoleName
		}

		// 更新启用状态
		if req.Enable != 0 {
			user.Enable = req.Enable
		}

		return db.Save(&user).Error
	})
}

// DeleteUser 删除用户
func (userService *UserService) DeleteUser(req request.DeleteUserRequest) error {
	// 🚀 使用数据库连接池进行事务操作，确保删除的安全性
	return dbpool.ExecuteDBTransaction("user_delete", func(db *gorm.DB) error {
		// 检查用户是否存在
		var user sys.SysUser
		if err := db.First(&user, req.ID).Error; err != nil {
			return errors.New("用户不存在")
		}

		// 检查是否是超级管理员
		if user.Role.RoleName == "superadmin" {
			return errors.New("不能删除超级管理员")
		}

		return db.Delete(&user).Error
	})
}

// ChangeUserStatus 修改用户状态
func (userService *UserService) ChangeUserStatus(req request.ChangeUserStatusRequest) error {
	// 检查用户是否存在
	var user sys.SysUser
	// 🚀 使用数据库连接池查询用户
	if err := dbpool.ExecuteDBOperationAsyncAndWait("user_query_by_id", func(db *gorm.DB) error {
		return db.First(&user, req.ID).Error
	}); err != nil {
		return errors.New("用户不存在")
	}

	// 检查是否是超级管理员
	if user.Role.RoleName == "superadmin" {
		return errors.New("不能修改超级管理员状态")
	}

	// 🚀 使用数据库连接池更新用户状态
	return dbpool.ExecuteDBOperationAsyncAndWait("user_enable_update", func(db *gorm.DB) error {
		return db.Model(&user).Update("enable", req.Enable).Error
	})
}
